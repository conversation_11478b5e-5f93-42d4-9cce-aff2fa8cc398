// Package config 提供 Aquila 系统的配置管理功能
package config

import (
	"fmt"
	"io/fs"
	"os"
	"path/filepath"
	"strings"
	"sync"
	"time"

	"aquila/internal/types"

	"github.com/fsnotify/fsnotify"
	"gopkg.in/yaml.v3"
)

// ConfigManager 配置管理器接口
// 定义配置加载和管理的核心功能
type ConfigManager interface {
	// LoadConfig 加载配置文件
	LoadConfig() (*Config, error)

	// GetUDPConfigs 获取UDP配置列表
	GetUDPConfigs() []types.UDPEndpoint

	// GetPDXPFrameTypes 获取PDXP帧类型配置列表
	GetPDXPFrameTypes() []types.PDXPFrameType

	// GetPDXPSchema 根据键名获取PDXP模式
	GetPDXPSchema(frameKey string) (*types.PDXPSchema, error)

	// ReloadConfig 重新加载配置
	ReloadConfig() error

	// WatchConfigChanges 监听配置变更，返回配置变更事件通道
	WatchConfigChanges() <-chan ConfigChangeEvent

	// StopWatching 停止配置变更监听
	StopWatching()

	// GetConfig 获取当前配置
	GetConfig() *Config

	// GetConfigStats 获取配置统计信息
	GetConfigStats() ConfigStats
}

// ConfigChangeEvent 配置变更事件
type ConfigChangeEvent struct {
	Type        ConfigChangeType `json:"type"`         // 变更类型
	Source      string           `json:"source"`       // 变更源
	Timestamp   time.Time        `json:"timestamp"`    // 变更时间
	Config      *Config          `json:"config"`       // 新配置
	ChangePaths []string         `json:"change_paths"` // 变更路径列表
	Error       error            `json:"error"`        // 错误信息
}

// ConfigChangeType 配置变更类型
type ConfigChangeType string

const (
	ConfigChangeTypeReload  ConfigChangeType = "reload"  // 配置重载
	ConfigChangeTypeModify  ConfigChangeType = "modify"  // 配置修改
	ConfigChangeTypeError   ConfigChangeType = "error"   // 配置错误
	ConfigChangeTypeStarted ConfigChangeType = "started" // 监听启动
	ConfigChangeTypeStopped ConfigChangeType = "stopped" // 监听停止
)

// Config 应用程序完整配置
type Config struct {
	types.AppConfig `yaml:",inline"` // 嵌入完整的应用配置

	// 配置文件元数据
	ConfigPath string    `json:"config_path"` // 配置文件路径
	LoadTime   time.Time `json:"load_time"`   // 加载时间
	Version    string    `json:"version"`     // 配置版本

	// 内部字段
	schemas map[string]*types.PDXPSchema `json:"-"` // PDXP模式缓存
}

// ConfigWatcher 配置变更监听器接口
type ConfigWatcher interface {
	OnConfigChanged(config *Config) error
}

// Manager 配置管理器
type Manager struct {
	config        *Config
	configPath    string
	schemaDir     string
	mutex         sync.RWMutex
	watchers      []ConfigWatcher
	isWatching    bool
	stopWatching  chan struct{}
	logger        types.Logger
	changeChannel chan ConfigChangeEvent   // 配置变更事件通道
	subscribers   []chan ConfigChangeEvent // 订阅者通道列表
	fsWatcher     *fsnotify.Watcher        // 文件系统监听器
}

// NewManager 创建配置管理器
func NewManager(configPath, schemaDir string, logger types.Logger) *Manager {
	return &Manager{
		configPath:    configPath,
		schemaDir:     schemaDir,
		watchers:      make([]ConfigWatcher, 0),
		stopWatching:  make(chan struct{}),
		logger:        logger,
		changeChannel: make(chan ConfigChangeEvent, 100), // 缓冲通道
		subscribers:   make([]chan ConfigChangeEvent, 0),
	}
}

// LoadConfig 加载配置文件
func (m *Manager) LoadConfig() (*Config, error) {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	// 检查配置文件是否存在
	if _, err := os.Stat(m.configPath); os.IsNotExist(err) {
		return nil, fmt.Errorf("配置文件不存在: %s", m.configPath)
	}

	// 读取配置文件
	data, err := os.ReadFile(m.configPath)
	if err != nil {
		return nil, fmt.Errorf("读取配置文件失败: %w", err)
	}

	// 解析YAML配置
	config := &Config{
		ConfigPath: m.configPath,
		LoadTime:   time.Now(),
		schemas:    make(map[string]*types.PDXPSchema),
	}

	if err := yaml.Unmarshal(data, config); err != nil {
		return nil, fmt.Errorf("解析配置文件失败: %w", err)
	}

	// 验证配置
	if err := m.validateConfig(config); err != nil {
		return nil, fmt.Errorf("配置验证失败: %w", err)
	}

	// 加载PDXP协议模式
	if err := m.loadPDXPSchemas(config); err != nil {
		return nil, fmt.Errorf("加载PDXP模式失败: %w", err)
	}

	m.config = config
	m.logger.Info("配置加载成功", "path", m.configPath, "udp_endpoints", len(config.UDP), "pdxp_frames", len(config.PDXP))

	return config, nil
}

// validateConfig 验证配置
func (m *Manager) validateConfig(config *Config) error {
	// 验证UDP配置
	if err := m.validateUDPConfig(config.UDP); err != nil {
		return fmt.Errorf("UDP配置错误: %w", err)
	}

	// 验证PDXP配置
	if err := m.validatePDXPConfig(config.PDXP); err != nil {
		return fmt.Errorf("PDXP配置错误: %w", err)
	}

	// 验证数据源引用一致性
	if err := m.validateSourceReferences(config); err != nil {
		return fmt.Errorf("数据源引用错误: %w", err)
	}

	return nil
}

// validateSourceReferences 验证数据源引用一致性
func (m *Manager) validateSourceReferences(config *Config) error {
	// 收集所有UDP端点名称
	udpSources := make(map[string]bool)
	for _, endpoint := range config.UDP {
		udpSources[endpoint.Name] = true
	}

	// 检查PDXP帧类型的数据源引用
	for _, frameType := range config.PDXP {
		if !udpSources[frameType.Src] {
			return fmt.Errorf("PDXP帧类型'%s'引用了不存在的数据源'%s'",
				frameType.Name, frameType.Src)
		}
	}

	return nil
}

// validateUDPConfig 验证UDP配置
func (m *Manager) validateUDPConfig(udpEndpoints []types.UDPEndpoint) error {
	if len(udpEndpoints) == 0 {
		return fmt.Errorf("UDP配置不能为空")
	}

	// 检查名称重复
	names := make(map[string]bool)
	for _, endpoint := range udpEndpoints {
		if names[endpoint.Name] {
			return fmt.Errorf("UDP端点名称'%s'重复", endpoint.Name)
		}
		names[endpoint.Name] = true

		// 验证端口范围
		if endpoint.Port < 1 || endpoint.Port > 65535 {
			return fmt.Errorf("UDP端点'%s'的端口号必须在1-65535范围内", endpoint.Name)
		}

		// 验证地址格式（简单检查）
		if endpoint.Addr == "" {
			return fmt.Errorf("UDP端点'%s'的地址不能为空", endpoint.Name)
		}
	}

	return nil
}

// validatePDXPConfig 验证PDXP配置
func (m *Manager) validatePDXPConfig(pdxpFrames []types.PDXPFrameType) error {
	// PDXP配置可以为空
	if len(pdxpFrames) == 0 {
		return nil
	}

	// 检查名称重复
	names := make(map[string]bool)
	for _, frame := range pdxpFrames {
		if names[frame.Name] {
			return fmt.Errorf("PDXP帧类型名称'%s'重复", frame.Name)
		}
		names[frame.Name] = true

		// 验证必要字段
		if frame.Key == "" {
			return fmt.Errorf("PDXP帧类型'%s'的键不能为空", frame.Name)
		}
		if frame.Src == "" {
			return fmt.Errorf("PDXP帧类型'%s'的源不能为空", frame.Name)
		}
		if len(frame.BIDs) == 0 {
			return fmt.Errorf("PDXP帧类型'%s'的BID列表不能为空", frame.Name)
		}
	}

	return nil
}

// loadPDXPSchemas 加载PDXP协议模式
func (m *Manager) loadPDXPSchemas(config *Config) error {
	if _, err := os.Stat(m.schemaDir); os.IsNotExist(err) {
		return fmt.Errorf("PDXP模式目录不存在: %s", m.schemaDir)
	}

	// 首先加载Header模式（所有帧的共有头）
	if err := m.loadHeaderSchema(config); err != nil {
		return fmt.Errorf("加载Header模式失败: %w", err)
	}

	// 遍历模式目录加载其他模式
	return filepath.WalkDir(m.schemaDir, func(path string, d fs.DirEntry, err error) error {
		if err != nil {
			return err
		}

		// 只处理.yaml文件，跳过规范文档
		if d.IsDir() || !strings.HasSuffix(strings.ToLower(path), ".yaml") {
			return nil
		}

		// 跳过PDXP规范文档文件，但保留Header.yaml
		fileName := filepath.Base(path)
		if strings.HasPrefix(strings.ToLower(fileName), "pdxp") {
			// 使用Info级别日志，因为Logger接口没有Debug方法
			m.logger.Info("跳过PDXP规范文档", "file", path)
			return nil
		}

		// 跳过Header.yaml，因为已经在loadHeaderSchema中单独处理
		if strings.ToLower(fileName) == "header.yaml" {
			m.logger.Info("跳过Header文件（已单独加载）", "file", path)
			return nil
		}

		// 读取模式文件
		data, err := os.ReadFile(path)
		if err != nil {
			return fmt.Errorf("读取模式文件失败 %s: %w", path, err)
		}

		// 解析模式定义
		schema := &types.PDXPSchema{}
		if err := yaml.Unmarshal(data, schema); err != nil {
			return fmt.Errorf("解析模式文件失败 %s: %w", path, err)
		}

		// 获取文件名作为模式键名（fileName已在上面定义）
		schemaKey := strings.TrimSuffix(fileName, filepath.Ext(fileName))

		// 验证模式是否被引用
		isUsed := false
		for _, frameType := range config.PDXP {
			if frameType.Key == schemaKey {
				isUsed = true
				break
			}
		}

		if isUsed {
			config.schemas[schemaKey] = schema
			m.logger.Info("加载PDXP模式", "key", schemaKey, "file", path)
		}

		return nil
	})
}

// loadHeaderSchema 加载PDXP Header模式
func (m *Manager) loadHeaderSchema(config *Config) error {
	headerPath := filepath.Join(m.schemaDir, "Header.yaml")

	// 检查Header文件是否存在
	if _, err := os.Stat(headerPath); os.IsNotExist(err) {
		m.logger.Warn("Header模式文件不存在，将跳过", "path", headerPath)
		return nil
	}

	// 读取Header模式文件
	data, err := os.ReadFile(headerPath)
	if err != nil {
		return fmt.Errorf("读取Header模式文件失败 %s: %w", headerPath, err)
	}

	// 解析Header模式定义
	schema := &types.PDXPSchema{}
	if err := yaml.Unmarshal(data, schema); err != nil {
		return fmt.Errorf("解析Header模式文件失败 %s: %w", headerPath, err)
	}

	// 存储Header模式
	config.schemas["Header"] = schema
	m.logger.Info("加载PDXP Header模式", "file", headerPath)

	return nil
}

// GetConfig 获取当前配置
func (m *Manager) GetConfig() *Config {
	m.mutex.RLock()
	defer m.mutex.RUnlock()
	return m.config
}

// GetPDXPSchema 根据键名获取PDXP模式
func (m *Manager) GetPDXPSchema(key string) (*types.PDXPSchema, error) {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	if m.config == nil {
		return nil, fmt.Errorf("配置尚未加载")
	}

	schema, exists := m.config.schemas[key]
	if !exists {
		return nil, fmt.Errorf("未找到PDXP模式: %s", key)
	}

	return schema, nil
}

// ReloadConfig 重新加载配置
func (m *Manager) ReloadConfig() error {
	m.logger.Info("开始重新加载配置")

	// 加载新配置
	newConfig, err := m.LoadConfig()
	if err != nil {
		return fmt.Errorf("重新加载配置失败: %w", err)
	}

	// 通知所有监听器
	for _, watcher := range m.watchers {
		if err := watcher.OnConfigChanged(newConfig); err != nil {
			m.logger.Warn("配置变更通知失败", "error", err.Error())
		}
	}

	m.logger.Info("配置重新加载完成")
	return nil
}

// AddWatcher 添加配置变更监听器
func (m *Manager) AddWatcher(watcher ConfigWatcher) {
	m.mutex.Lock()
	defer m.mutex.Unlock()
	m.watchers = append(m.watchers, watcher)
}

// RemoveWatcher 移除配置变更监听器
func (m *Manager) RemoveWatcher(watcher ConfigWatcher) {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	for i, w := range m.watchers {
		// 使用指针比较
		if w == watcher {
			m.watchers = append(m.watchers[:i], m.watchers[i+1:]...)
			break
		}
	}
}

// StartWatching 启动配置文件监听
func (m *Manager) StartWatching() error {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	if m.isWatching {
		return fmt.Errorf("配置监听已经启动")
	}

	// 创建文件系统监听器
	var err error
	m.fsWatcher, err = fsnotify.NewWatcher()
	if err != nil {
		return fmt.Errorf("创建文件系统监听器失败: %w", err)
	}

	// 添加配置文件到监听列表
	if err := m.fsWatcher.Add(m.configPath); err != nil {
		m.fsWatcher.Close()
		return fmt.Errorf("添加配置文件监听失败: %w", err)
	}

	// 添加PDXP模式目录到监听列表
	if _, err := os.Stat(m.schemaDir); err == nil {
		if err := m.fsWatcher.Add(m.schemaDir); err != nil {
			m.logger.Warn("添加PDXP模式目录监听失败", "error", err.Error(), "dir", m.schemaDir)
		}
	}

	m.isWatching = true
	go m.watchConfigFiles()

	// 发送启动事件
	event := ConfigChangeEvent{
		Type:      ConfigChangeTypeStarted,
		Source:    "config_manager",
		Timestamp: time.Now(),
		Config:    m.config,
	}
	go m.notifySubscribers(event)

	m.logger.Info("启动配置文件热重载监听", "config_path", m.configPath, "schema_dir", m.schemaDir)
	return nil
}

// StopWatching 停止配置文件监听
func (m *Manager) StopWatching() {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	if !m.isWatching {
		return
	}

	m.isWatching = false

	// 安全关闭停止通道
	select {
	case <-m.stopWatching:
		// 通道已关闭
	default:
		close(m.stopWatching)
	}
	m.stopWatching = make(chan struct{})

	// 关闭文件系统监听器
	if m.fsWatcher != nil {
		if err := m.fsWatcher.Close(); err != nil {
			m.logger.Warn("关闭文件系统监听器失败", "error", err.Error())
		}
		m.fsWatcher = nil
	}

	// 安全关闭所有订阅者通道
	for i, subscriber := range m.subscribers {
		select {
		case <-subscriber:
			// 通道已关闭
		default:
			close(subscriber)
		}
		m.subscribers[i] = nil // 避免内存泄漏
	}
	m.subscribers = make([]chan ConfigChangeEvent, 0)

	m.logger.Info("停止配置文件热重载监听")
}

// watchConfigFiles 监听配置文件变更并发送事件（使用fsnotify）
func (m *Manager) watchConfigFiles() {
	defer func() {
		// 发送停止事件
		event := ConfigChangeEvent{
			Type:      ConfigChangeTypeStopped,
			Source:    "fsnotify_watcher",
			Timestamp: time.Now(),
		}
		go m.notifySubscribers(event)
	}()

	// 检查fsWatcher是否已初始化
	if m.fsWatcher == nil {
		m.logger.Error("文件系统监听器未初始化")
		return
	}

	for {
		select {
		case <-m.stopWatching:
			return

		case event, ok := <-m.fsWatcher.Events:
			if !ok {
				m.logger.Error("文件系统监听器事件通道已关闭")
				return
			}

			// 只处理写入和创建事件
			if event.Has(fsnotify.Write) || event.Has(fsnotify.Create) {
				m.handleFileChange(event.Name)
			}

		case err, ok := <-m.fsWatcher.Errors:
			if !ok {
				m.logger.Error("文件系统监听器错误通道已关闭")
				return
			}
			m.logger.Error("文件系统监听错误", "error", err.Error())

			// 发送错误事件
			changeEvent := ConfigChangeEvent{
				Type:      ConfigChangeTypeError,
				Source:    "fsnotify_watcher",
				Timestamp: time.Now(),
				Error:     err,
			}
			go m.notifySubscribers(changeEvent)
		}
	}
}

// handleFileChange 处理文件变更事件
func (m *Manager) handleFileChange(filePath string) {
	// 添加防抖机制，避免短时间内重复触发
	time.Sleep(100 * time.Millisecond)

	// 判断是主配置文件还是PDXP模式文件
	var changeType ConfigChangeType
	var changePaths []string

	if filePath == m.configPath {
		changeType = ConfigChangeTypeReload
		changePaths = []string{filePath}
		m.logger.Info("检测到主配置文件变更", "file", filePath)
	} else if strings.HasPrefix(filePath, m.schemaDir) && strings.HasSuffix(filePath, ".yaml") {
		changeType = ConfigChangeTypeModify
		changePaths = []string{filePath}
		m.logger.Info("检测到PDXP模式文件变更", "file", filePath)
	} else {
		// 忽略其他文件变更
		return
	}

	// 尝试重新加载配置
	newConfig, reloadErr := m.LoadConfig()

	event := ConfigChangeEvent{
		Type:        changeType,
		Source:      "fsnotify_watcher",
		Timestamp:   time.Now(),
		Config:      newConfig,
		ChangePaths: changePaths,
	}

	if reloadErr != nil {
		event.Type = ConfigChangeTypeError
		event.Error = reloadErr
		m.logger.Error("热重载配置失败", "error", reloadErr.Error(), "file", filePath)
	} else {
		// 通知所有监听器
		for _, watcher := range m.watchers {
			if watcherErr := watcher.OnConfigChanged(newConfig); watcherErr != nil {
				m.logger.Warn("配置变更通知失败", "error", watcherErr.Error())
			}
		}
		m.logger.Info("配置热重载成功", "file", filePath)
	}

	// 发送配置变更事件
	go m.notifySubscribers(event)
}

// ValidateConfigFile 验证配置文件而不加载
func ValidateConfigFile(configPath string) error {
	if _, err := os.Stat(configPath); os.IsNotExist(err) {
		return fmt.Errorf("配置文件不存在: %s", configPath)
	}

	data, err := os.ReadFile(configPath)
	if err != nil {
		return fmt.Errorf("读取配置文件失败: %w", err)
	}

	config := &Config{}
	if err := yaml.Unmarshal(data, config); err != nil {
		return fmt.Errorf("解析配置文件失败: %w", err)
	}

	// 创建临时Manager实例来使用验证方法
	tempManager := &Manager{}

	if err := tempManager.validateUDPConfig(config.UDP); err != nil {
		return fmt.Errorf("UDP配置错误: %w", err)
	}

	if err := tempManager.validatePDXPConfig(config.PDXP); err != nil {
		return fmt.Errorf("PDXP配置错误: %w", err)
	}

	return nil
}

// GetConfigStats 获取配置统计信息
func (m *Manager) GetConfigStats() ConfigStats {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	if m.config == nil {
		return ConfigStats{}
	}

	stats := ConfigStats{
		ConfigPath:        m.config.ConfigPath,
		LoadTime:          m.config.LoadTime,
		IsWatching:        m.isWatching,
		TotalUDPEndpoints: len(m.config.UDP),
		TotalPDXPFrames:   len(m.config.PDXP),
		TotalPDXPSchemas:  len(m.config.schemas),
		WatcherCount:      len(m.watchers),
	}

	// 统计启用的端点和帧类型
	for _, endpoint := range m.config.UDP {
		if endpoint.Enable {
			stats.EnabledUDPEndpoints++
		}
	}

	for _, frameType := range m.config.PDXP {
		if frameType.Enable {
			stats.EnabledPDXPFrames++
		}
	}

	return stats
}

// GetUDPConfigs 获取UDP配置列表
func (m *Manager) GetUDPConfigs() []types.UDPEndpoint {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	if m.config == nil {
		return []types.UDPEndpoint{}
	}

	return m.config.UDP
}

// GetPDXPFrameTypes 获取PDXP帧类型配置列表
func (m *Manager) GetPDXPFrameTypes() []types.PDXPFrameType {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	if m.config == nil {
		return []types.PDXPFrameType{}
	}

	return m.config.PDXP
}

// WatchConfigChanges 监听配置变更，返回配置变更事件通道
func (m *Manager) WatchConfigChanges() <-chan ConfigChangeEvent {
	m.mutex.Lock()

	// 创建新的订阅者通道
	subscriber := make(chan ConfigChangeEvent, 100)
	m.subscribers = append(m.subscribers, subscriber)

	// 检查是否需要启动监听
	needStartWatching := !m.isWatching
	m.mutex.Unlock()

	// 如果还未启动监听，自动启动（在锁外调用避免死锁）
	if needStartWatching {
		if err := m.StartWatching(); err != nil {
			m.logger.Error("启动配置热重载监听失败", "error", err.Error())
			// 发送错误事件
			event := ConfigChangeEvent{
				Type:      ConfigChangeTypeError,
				Source:    "config_manager",
				Timestamp: time.Now(),
				Error:     err,
			}
			// 异步发送，避免死锁
			go func() {
				select {
				case subscriber <- event:
				default:
					close(subscriber)
				}
			}()
		}
	}

	return subscriber
}

// notifySubscribers 通知所有订阅者
func (m *Manager) notifySubscribers(event ConfigChangeEvent) {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	// 创建有效订阅者的副本，避免在迭代过程中修改切片
	validSubscribers := make([]chan ConfigChangeEvent, 0, len(m.subscribers))

	// 向所有订阅者发送事件，非阻塞发送
	for i, subscriber := range m.subscribers {
		if subscriber == nil {
			continue
		}

		select {
		case subscriber <- event:
			// 成功发送，保留该订阅者
			validSubscribers = append(validSubscribers, subscriber)
		default:
			// 通道已满或关闭，安全关闭并移除该订阅者
			select {
			case <-subscriber:
				// 通道已关闭
			default:
				close(subscriber)
			}
			m.subscribers[i] = nil // 标记为无效
		}
	}

	// 更新订阅者列表，只保留有效的订阅者
	m.subscribers = validSubscribers
}

// ConfigStats 配置统计信息
type ConfigStats struct {
	ConfigPath          string    `json:"config_path"`           // 配置文件路径
	LoadTime            time.Time `json:"load_time"`             // 加载时间
	IsWatching          bool      `json:"is_watching"`           // 是否正在监听
	TotalUDPEndpoints   int       `json:"total_udp_endpoints"`   // UDP端点总数
	EnabledUDPEndpoints int       `json:"enabled_udp_endpoints"` // 启用的UDP端点数
	TotalPDXPFrames     int       `json:"total_pdxp_frames"`     // PDXP帧类型总数
	EnabledPDXPFrames   int       `json:"enabled_pdxp_frames"`   // 启用的PDXP帧类型数
	TotalPDXPSchemas    int       `json:"total_pdxp_schemas"`    // PDXP模式总数
	WatcherCount        int       `json:"watcher_count"`         // 监听器数量
}
