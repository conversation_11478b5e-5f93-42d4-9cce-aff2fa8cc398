<?xml version="1.0" encoding="utf-8"?>
<FrameStructs>
	<FrameStruct ID="00" Description="PDXP头" FrameLength="32" DataTableName="PDXPHead">
		<Version Description="版本" Multiple="1" Function="" DataBegin="0" DataLength="1" HasSign="false" ColumnName="ver" DataType="int(11)"> </Version>
		<MID Description="任务标志" Multiple="1" Function="" DataBegin="1" DataLength="2" HasSign="false" ColumnName="mid" DataType="int(11)"> </MID>	
		<SID Description="信源地址" Multiple="1" Function="" DataBegin="3" DataLength="4" HasSign="false" ColumnName="sid" DataType="int(11)"> </SID>	
		<DID Description="信宿地址" Multiple="1" Function="" DataBegin="7" DataLength="4" HasSign="false" ColumnName="did" DataType="int(11)"> </DID>	
		<BID Description="数据标志" Multiple="1" Function="" DataBegin="11" DataLength="4" HasSign="false" ColumnName="bid" DataType="int(11)"> </BID>	
		<Number Description="包序号" Multiple="1" Function="" DataBegin="15" DataLength="4" HasSign="false" ColumnName="no" DataType="int(11)"> </Number>	
		<Flag Description="数据处理标志" Multiple="1" Function="" DataBegin="19" DataLength="1" HasSign="false" ColumnName="flag" DataType="int(11)"> </Flag>	
		<Reserved Description="保留字段" Multiple="1" Function="" DataBegin="20" DataLength="4" HasSign="false" ColumnName="empt" DataType="int(11)"> </Reserved>	
		<Date Description="发送日期" Multiple="1" Function="" DataBegin="24" DataLength="2" HasSign="false" ColumnName="date" DataType="int(11)"> </Date>
		<Time Description="发送时标" Multiple="1" Function="" DataBegin="26" DataLength="4" HasSign="false" ColumnName="time" DataType="int(11)"> </Time>		
		<Length Description="数据阈长度" Multiple="1" Function="" DataBegin="30" DataLength="2" HasSign="false" ColumnName="length" DataType="int(11)"> </Length>				
	</FrameStruct>
	<FrameStruct ID="01" Description="GXDeviceFirst" FrameLength="32" DataTableName="out_gx_table_first">
		<T Description="当前时刻" Multiple="1" Function="" DataBegin="32" DataLength="4" HasSign="false" ColumnName="t" DataType="int(11)"> </T>
		<ZT Description="设备状态" Multiple="1" Function="" DataBegin="36" DataLength="1" HasSign="false" ColumnName="zt" DataType="int(11)"> </ZT>	
		<A Description="方位角" Multiple="0.000000168" Function="" DataBegin="37" DataLength="4" HasSign="true" ColumnName="a" DataType="double(255, 2)"> </A>	
		<E Description="俯仰角" Multiple="0.000000168" Function="" DataBegin="41" DataLength="4" HasSign="true" ColumnName="e" DataType="double(255, 2)"> </E>	
		<TargetNum Description="目标总数" Multiple="1" Function="" DataBegin="45" DataLength="1" HasSign="false" ColumnName="num" DataType="int(11)"> </TargetNum>	
		<DA1 Description="方位脱靶量" Multiple="1" Function="" DataBegin="46" DataLength="4" HasSign="true" ColumnName="da1" DataType="double(255, 2)"> </DA1>	
		<DE1 Description="俯仰脱靶量" Multiple="1" Function="" DataBegin="50" DataLength="4" HasSign="true" ColumnName="de1" DataType="double(255, 2)"> </DE1>	
		<V1 Description="测速值" Multiple="1" Function="" DataBegin="54" DataLength="4" HasSign="true" ColumnName="v1" DataType="double(255, 2)"> </V1>	
		<R1 Description="测距值" Multiple="1" Function="" DataBegin="58" DataLength="4" HasSign="true" ColumnName="r1" DataType="double(255, 2)"> </R1>
		<L1 Description="亮度" Multiple="1" Function="" DataBegin="62" DataLength="2" HasSign="true" ColumnName="l1" DataType="double(255, 2)"> </L1>		
	</FrameStruct>
	<FrameStruct ID="02" Description="GXDeviceSecond" FrameLength="32" DataTableName="out_gx_table_second">
		<T Description="当前时刻" Multiple="1" Function="" DataBegin="32" DataLength="4" HasSign="false" ColumnName="t" DataType="int(11)"> </T>
		<ZT Description="设备状态" Multiple="1" Function="" DataBegin="36" DataLength="1" HasSign="false" ColumnName="zt" DataType="int(11)"> </ZT>	
		<A Description="方位角" Multiple="0.000000168" Function="" DataBegin="37" DataLength="4" HasSign="true" ColumnName="a" DataType="double(255, 2)"> </A>	
		<E Description="俯仰角" Multiple="0.000000168" Function="" DataBegin="41" DataLength="4" HasSign="true" ColumnName="e" DataType="double(255, 2)"> </E>	
		<TargetNum Description="目标总数" Multiple="1" Function="" DataBegin="45" DataLength="1" HasSign="false" ColumnName="num" DataType="int(11)"> </TargetNum>	
		<DA1 Description="方位脱靶量" Multiple="1" Function="" DataBegin="46" DataLength="4" HasSign="true" ColumnName="da1" DataType="double(255, 2)"> </DA1>	
		<DE1 Description="俯仰脱靶量" Multiple="1" Function="" DataBegin="50" DataLength="4" HasSign="true" ColumnName="de1" DataType="double(255, 2)"> </DE1>	
		<V1 Description="测速值" Multiple="1" Function="" DataBegin="54" DataLength="4" HasSign="true" ColumnName="v1" DataType="double(255, 2)"> </V1>	
		<R1 Description="测距值" Multiple="1" Function="" DataBegin="58" DataLength="4" HasSign="true" ColumnName="r1" DataType="double(255, 2)"> </R1>
		<L1 Description="亮度" Multiple="1" Function="" DataBegin="62" DataLength="2" HasSign="true" ColumnName="l1" DataType="double(255, 2)"> </L1>		
	</FrameStruct>
	<FrameStruct ID="03" Description="MCLDDeviceFirst" FrameLength="32" DataTableName="out_mcld_table_first">
		<N Description="目标数" Multiple="1" Function="" DataBegin="32" DataLength="1" HasSign="false" ColumnName="n" DataType="int(11)"> </N>
		<MB1 Description="标志码" Multiple="1" Function="" DataBegin="33" DataLength="1" HasSign="false" ColumnName="mb1" DataType="int(11)"> </MB1>
		<T Description="当前时刻" Multiple="1" Function="" DataBegin="34" DataLength="4" HasSign="false" ColumnName="t" DataType="int(11)"> </T>
		<ZT Description="设备状态" Multiple="1" Function="" DataBegin="38" DataLength="1" HasSign="false" ColumnName="zt" DataType="int(11)"> </ZT>	
		<V Description="测速值" Multiple="0.001" Function="" DataBegin="39" DataLength="4" HasSign="true" ColumnName="v" DataType="double(255, 2)"> </V>	
		<R Description="径向距离值" Multiple="1" Function="" DataBegin="43" DataLength="4" HasSign="false" ColumnName="r" DataType="double(255, 2)"> </R>	
		<A Description="方位角" Multiple="0.000000168" Function="" DataBegin="47" DataLength="4" HasSign="false" ColumnName="a" DataType="double(255, 2)"> </A>	
		<E Description="俯仰角" Multiple="0.000000168" Function="" DataBegin="51" DataLength="4" HasSign="false" ColumnName="e" DataType="double(255, 2)"> </E>	
		<DA Description="方位角动态滞后修正量" Multiple="1" Function="" DataBegin="55" DataLength="1" HasSign="true" ColumnName="da" DataType="double(255, 2)"> </DA>	
		<DE Description="俯仰角动态滞后修正量" Multiple="1" Function="" DataBegin="56" DataLength="1" HasSign="true" ColumnName="de" DataType="double(255, 2)"> </DE>	
		<AGC Description="接收机自动增益控制量" Multiple="1" Function="" DataBegin="57" DataLength="1" HasSign="true" ColumnName="agc" DataType="double(255, 2)"> </AGC>	
		<RCS Description="目标的窄带RCS幅度值" Multiple="1" Function="" DataBegin="58" DataLength="1" HasSign="true" ColumnName="rcs" DataType="double(255, 2)"> </RCS>
		<X Description="X" Multiple="0.01" Function="" DataBegin="59" DataLength="4" HasSign="true" ColumnName="X" DataType="double(255, 2)"> </X>
		<Y Description="Y" Multiple="0.01" Function="" DataBegin="63" DataLength="4" HasSign="true" ColumnName="Y" DataType="double(255, 2)"> </Y>
		<Z Description="Z" Multiple="0.01" Function="" DataBegin="67" DataLength="4" HasSign="true" ColumnName="Z" DataType="double(255, 2)"> </Z>
		<VX Description="VX" Multiple="0.001" Function="" DataBegin="71" DataLength="4" HasSign="true" ColumnName="VX" DataType="double(255, 3)"> </VX>
		<VY Description="VY" Multiple="0.001" Function="" DataBegin="75" DataLength="4" HasSign="true" ColumnName="VY" DataType="double(255, 3)"> </VY>
		<VZ Description="VZ" Multiple="0.001" Function="" DataBegin="79" DataLength="4" HasSign="true" ColumnName="VZ" DataType="double(255, 3)"> </VZ>
	</FrameStruct>
	<FrameStruct ID="04" Description="MCLDDeviceSecond" FrameLength="32" DataTableName="out_mcld_table_second">
		<N Description="目标数" Multiple="1" Function="" DataBegin="32" DataLength="1" HasSign="false" ColumnName="n" DataType="int(11)"> </N>
		<MB1 Description="标志码" Multiple="1" Function="" DataBegin="33" DataLength="1" HasSign="false" ColumnName="mb1" DataType="int(11)"> </MB1>
		<T Description="当前时刻" Multiple="1" Function="" DataBegin="34" DataLength="4" HasSign="false" ColumnName="t" DataType="int(11)"> </T>
		<ZT Description="设备状态" Multiple="1" Function="" DataBegin="38" DataLength="1" HasSign="false" ColumnName="zt" DataType="int(11)"> </ZT>	
		<V Description="测速值" Multiple="1" Function="" DataBegin="39" DataLength="4" HasSign="true" ColumnName="v" DataType="double(255, 2)"> </V>	
		<R Description="径向距离值" Multiple="1" Function="" DataBegin="43" DataLength="4" HasSign="true" ColumnName="r" DataType="double(255, 2)"> </R>	
		<A Description="方位角" Multiple="0.000000168" Function="" DataBegin="47" DataLength="4" HasSign="false" ColumnName="a" DataType="double(255, 9)"> </A>	
		<E Description="俯仰角" Multiple="0.000000168" Function="" DataBegin="51" DataLength="4" HasSign="false" ColumnName="e" DataType="double(255, 9)"> </E>	
		<DA Description="方位角动态滞后修正量" Multiple="1" Function="" DataBegin="55" DataLength="1" HasSign="true" ColumnName="da" DataType="double(255, 2)"> </DA>	
		<DE Description="俯仰角动态滞后修正量" Multiple="1" Function="" DataBegin="56" DataLength="1" HasSign="true" ColumnName="de" DataType="double(255, 2)"> </DE>	
		<AGC Description="接收机自动增益控制量" Multiple="1" Function="" DataBegin="57" DataLength="1" HasSign="true" ColumnName="agc" DataType="double(255, 2)"> </AGC>	
		<RCS Description="目标的窄带RCS幅度值" Multiple="1" Function="" DataBegin="58" DataLength="1" HasSign="true" ColumnName="rcs" DataType="double(255, 2)"> </RCS>
		<X Description="X" Multiple="0.01" Function="" DataBegin="59" DataLength="4" HasSign="true" ColumnName="X" DataType="double(255, 2)"> </X>
		<Y Description="Y" Multiple="0.01" Function="" DataBegin="63" DataLength="4" HasSign="true" ColumnName="Y" DataType="double(255, 2)"> </Y>
		<Z Description="Z" Multiple="0.01" Function="" DataBegin="67" DataLength="4" HasSign="true" ColumnName="Z" DataType="double(255, 2)"> </Z>
		<VX Description="VX" Multiple="0.001" Function="" DataBegin="71" DataLength="4" HasSign="true" ColumnName="VX" DataType="double(255, 3)"> </VX>
		<VY Description="VY" Multiple="0.001" Function="" DataBegin="75" DataLength="4" HasSign="true" ColumnName="VY" DataType="double(255, 3)"> </VY>
		<VZ Description="VZ" Multiple="0.001" Function="" DataBegin="79" DataLength="4" HasSign="true" ColumnName="VZ" DataType="double(255, 3)"> </VZ>
	</FrameStruct>
	<FrameStruct ID="05" Description="CSLDDeviceFirst" FrameLength="32" DataTableName="out_csld_table_first">
		<T Description="当前时刻" Multiple="1" Function="" DataBegin="32" DataLength="4" HasSign="false" ColumnName="t" DataType="int(11)"> </T>
		<ZT Description="设备状态" Multiple="1" Function="" DataBegin="36" DataLength="1" HasSign="false" ColumnName="zt" DataType="int(11)"> </ZT>	
		<FDA Description="通道1有效测速数据" Multiple="1" Function="" DataBegin="37" DataLength="4" HasSign="true" ColumnName="fda" DataType="double(255, 2)"> </FDA>	
		<FDB Description="通道2有效测速数据" Multiple="1" Function="" DataBegin="41" DataLength="4" HasSign="true" ColumnName="fdb" DataType="double(255, 2)"> </FDB>	
		<A Description="方位角" Multiple="1" Function="" DataBegin="45" DataLength="4" HasSign="true" ColumnName="a" DataType="double(255, 2)"> </A>	
		<E Description="俯仰角" Multiple="1" Function="" DataBegin="49" DataLength="4" HasSign="true" ColumnName="e" DataType="double(255, 2)"> </E>	
		<UA Description="方位角误差电压" Multiple="1" Function="" DataBegin="53" DataLength="2" HasSign="true" ColumnName="ua" DataType="double(255, 2)"> </UA>	
		<UE Description="俯仰角误差电压" Multiple="1" Function="" DataBegin="55" DataLength="2" HasSign="true" ColumnName="ue" DataType="double(255, 2)"> </UE>	
		<AGCA Description="通道1接收信号电平" Multiple="1" Function="" DataBegin="57" DataLength="1" HasSign="true" ColumnName="agca" DataType="double(255, 2)"> </AGCA>	
		<AGCB Description="通道2接收信号电平" Multiple="1" Function="" DataBegin="58" DataLength="1" HasSign="true" ColumnName="agcb" DataType="double(255, 2)"> </AGCB>
	</FrameStruct>
	<FrameStruct ID="06" Description="CSLDDeviceSecond" FrameLength="32" DataTableName="out_csld_table_second">
		<T Description="当前时刻" Multiple="1" Function="" DataBegin="32" DataLength="4" HasSign="false" ColumnName="t" DataType="int(11)"> </T>
		<ZT Description="设备状态" Multiple="1" Function="" DataBegin="36" DataLength="1" HasSign="false" ColumnName="zt" DataType="int(11)"> </ZT>	
		<FDA Description="通道1有效测速数据" Multiple="1" Function="" DataBegin="37" DataLength="4" HasSign="true" ColumnName="fda" DataType="double(255, 2)"> </FDA>	
		<FDB Description="通道2有效测速数据" Multiple="1" Function="" DataBegin="41" DataLength="4" HasSign="true" ColumnName="fdb" DataType="double(255, 2)"> </FDB>	
		<A Description="方位角" Multiple="1" Function="" DataBegin="45" DataLength="4" HasSign="true" ColumnName="a" DataType="double(255, 2)"> </A>	
		<E Description="俯仰角" Multiple="1" Function="" DataBegin="49" DataLength="4" HasSign="true" ColumnName="e" DataType="double(255, 2)"> </E>	
		<UA Description="方位角误差电压" Multiple="1" Function="" DataBegin="53" DataLength="2" HasSign="true" ColumnName="ua" DataType="double(255, 2)"> </UA>	
		<UE Description="俯仰角误差电压" Multiple="1" Function="" DataBegin="55" DataLength="2" HasSign="true" ColumnName="ue" DataType="double(255, 2)"> </UE>	
		<AGCA Description="通道1接收信号电平" Multiple="1" Function="" DataBegin="57" DataLength="1" HasSign="true" ColumnName="agca" DataType="double(255, 2)"> </AGCA>	
		<AGCB Description="通道2接收信号电平" Multiple="1" Function="" DataBegin="58" DataLength="1" HasSign="true" ColumnName="agcb" DataType="double(255, 2)"> </AGCB>
	</FrameStruct>
	<FrameStruct ID="07" Description="KAAEFirst" FrameLength="32" DataTableName="out_ka_angle_table_first">
		<T Description="当前时刻" Multiple="1" Function="" DataBegin="32" DataLength="4" HasSign="false" ColumnName="t" DataType="int(11)"> </T>
		<ZT Description="设备状态" Multiple="1" Function="" DataBegin="36" DataLength="2" HasSign="true" ColumnName="zt" DataType="int(11)"> </ZT>	
		<A Description="方位角" Multiple="0.000000083819031715393" Function="" DataBegin="38" DataLength="4" HasSign="false" ColumnName="a" DataType="double(255, 5)"> </A>	
		<E Description="俯仰角" Multiple="0.000000083819031715393" Function="" DataBegin="42" DataLength="4" HasSign="false" ColumnName="e" DataType="double(255, 5)"> </E>		
		<DA1 Description="方位角动态滞后" Multiple="0.000152588" Function="" DataBegin="46" DataLength="2" HasSign="true" ColumnName="da1" DataType="double(255, 5)"> </DA1>	
		<DE1 Description="俯仰角动态滞后" Multiple="0.000152588" Function="" DataBegin="48" DataLength="2" HasSign="true" ColumnName="de1" DataType="double(255, 5)"> </DE1>	
		<DA2 Description="方位角脱靶" Multiple="0.001831055" Function="" DataBegin="50" DataLength="2" HasSign="true" ColumnName="da2" DataType="double(255, 5)"> </DA2>	
		<DE2 Description="俯仰角脱靶" Multiple="0.001831055" Function="" DataBegin="52" DataLength="2" HasSign="true" ColumnName="de2" DataType="double(255, 5)"> </DE2>
		<XZB Description="信噪比" Multiple="0.01" Function="" DataBegin="54" DataLength="2" HasSign="true" ColumnName="xzb" DataType="double(255, 2)"> </XZB>	
		<X Description="X" Multiple="0.01" Function="" DataBegin="56" DataLength="4" HasSign="true" ColumnName="X" DataType="double(255, 2)"> </X>
		<Y Description="Y" Multiple="0.01" Function="" DataBegin="60" DataLength="4" HasSign="true" ColumnName="Y" DataType="double(255, 2)"> </Y>
		<Z Description="Z" Multiple="0.01" Function="" DataBegin="64" DataLength="4" HasSign="true" ColumnName="Z" DataType="double(255, 2)"> </Z>
		<VX Description="VX" Multiple="0.001" Function="" DataBegin="68" DataLength="4" HasSign="true" ColumnName="VX" DataType="double(255, 3)"> </VX>
		<VY Description="VY" Multiple="0.001" Function="" DataBegin="72" DataLength="4" HasSign="true" ColumnName="VY" DataType="double(255, 3)"> </VY>
		<VZ Description="VZ" Multiple="0.001" Function="" DataBegin="76" DataLength="4" HasSign="true" ColumnName="VZ" DataType="double(255, 3)"> </VZ>
	</FrameStruct>

	<FrameStruct ID="08" Description="KAAESecond" FrameLength="32" DataTableName="out_ka_angle_table_second">
		<T Description="当前时刻" Multiple="1" Function="" DataBegin="32" DataLength="4" HasSign="false" ColumnName="t" DataType="int(11)"> </T>
		<ZT Description="设备状态" Multiple="1" Function="" DataBegin="36" DataLength="2" HasSign="true" ColumnName="zt" DataType="int(11)"> </ZT>	
		<A Description="方位角" Multiple="0.000000083819031715393" Function="" DataBegin="38" DataLength="4" HasSign="false" ColumnName="a" DataType="double(255, 5)"> </A>	
		<E Description="俯仰角" Multiple="0.000000083819031715393" Function="" DataBegin="42" DataLength="4" HasSign="false" ColumnName="e" DataType="double(255, 5)"> </E>		
		<DA1 Description="方位角动态滞后" Multiple="0.000152588" Function="" DataBegin="46" DataLength="2" HasSign="true" ColumnName="da1" DataType="double(255, 5)"> </DA1>	
		<DE1 Description="俯仰角动态滞后" Multiple="0.000152588" Function="" DataBegin="48" DataLength="2" HasSign="true" ColumnName="de1" DataType="double(255, 5)"> </DE1>	
		<DA2 Description="方位角脱靶" Multiple="0.001831055" Function="" DataBegin="50" DataLength="2" HasSign="true" ColumnName="da2" DataType="double(255, 5)"> </DA2>	
		<DE2 Description="俯仰角脱靶" Multiple="0.001831055" Function="" DataBegin="52" DataLength="2" HasSign="true" ColumnName="de2" DataType="double(255, 5)"> </DE2>
		<XZB Description="信噪比" Multiple="0.01" Function="" DataBegin="54" DataLength="2" HasSign="true" ColumnName="xzb" DataType="double(255, 2)"> </XZB>	
		<X Description="X" Multiple="0.01" Function="" DataBegin="56" DataLength="4" HasSign="true" ColumnName="X" DataType="double(255, 2)"> </X>
		<Y Description="Y" Multiple="0.01" Function="" DataBegin="60" DataLength="4" HasSign="true" ColumnName="Y" DataType="double(255, 2)"> </Y>
		<Z Description="Z" Multiple="0.01" Function="" DataBegin="64" DataLength="4" HasSign="true" ColumnName="Z" DataType="double(255, 2)"> </Z>
		<VX Description="VX" Multiple="0.001" Function="" DataBegin="68" DataLength="4" HasSign="true" ColumnName="VX" DataType="double(255, 3)"> </VX>
		<VY Description="VY" Multiple="0.001" Function="" DataBegin="72" DataLength="4" HasSign="true" ColumnName="VY" DataType="double(255, 3)"> </VY>
		<VZ Description="VZ" Multiple="0.001" Function="" DataBegin="76" DataLength="4" HasSign="true" ColumnName="VZ" DataType="double(255, 3)"> </VZ>
	</FrameStruct>
	<FrameStruct ID="09" Description="KARFirst" FrameLength="32" DataTableName="out_ka_distance_table_first">
		<T Description="当前时刻" Multiple="1" Function="" DataBegin="32" DataLength="4" HasSign="false" ColumnName="t" DataType="int(11)"> </T>
		<ZT Description="设备状态" Multiple="1" Function="" DataBegin="36" DataLength="2" HasSign="true" ColumnName="zt" DataType="int(11)"> </ZT>	
		<R Description="距离" Multiple="0.1" Function="" DataBegin="38" DataLength="8" HasSign="false" ColumnName="r" DataType="double(255, 1)"> </R>	
		<DT Description="弹地时差" Multiple="0.1" Function="" DataBegin="46" DataLength="8" HasSign="true" ColumnName="dt" DataType="double(255, 1)"> </DT>	
		<XZB Description="信噪比" Multiple="1" Function="" DataBegin="54" DataLength="2" HasSign="true" ColumnName="xzb" DataType="double(255, 2)"> </XZB>	
		<X Description="X" Multiple="0.01" Function="" DataBegin="56" DataLength="4" HasSign="true" ColumnName="X" DataType="double(255, 2)"> </X>
		<Y Description="Y" Multiple="0.01" Function="" DataBegin="60" DataLength="4" HasSign="true" ColumnName="Y" DataType="double(255, 2)"> </Y>
		<Z Description="Z" Multiple="0.01" Function="" DataBegin="64" DataLength="4" HasSign="true" ColumnName="Z" DataType="double(255, 2)"> </Z>
		<VX Description="VX" Multiple="0.001" Function="" DataBegin="68" DataLength="4" HasSign="true" ColumnName="VX" DataType="double(255, 3)"> </VX>
		<VY Description="VY" Multiple="0.001" Function="" DataBegin="72" DataLength="4" HasSign="true" ColumnName="VY" DataType="double(255, 3)"> </VY>
		<VZ Description="VZ" Multiple="0.001" Function="" DataBegin="76" DataLength="4" HasSign="true" ColumnName="VZ" DataType="double(255, 3)"> </VZ>
	</FrameStruct>
	<FrameStruct ID="10" Description="KARSecond" FrameLength="32" DataTableName="out_ka_distance_table_second">
		<T Description="当前时刻" Multiple="1" Function="" DataBegin="32" DataLength="4" HasSign="false" ColumnName="t" DataType="int(11)"> </T>
		<ZT Description="设备状态" Multiple="1" Function="" DataBegin="36" DataLength="2" HasSign="true" ColumnName="zt" DataType="int(11)"> </ZT>	
		<R Description="距离" Multiple="0.1" Function="" DataBegin="38" DataLength="8" HasSign="false" ColumnName="r" DataType="double(255, 1)"> </R>	
		<DT Description="弹地时差" Multiple="0.1" Function="" DataBegin="46" DataLength="8" HasSign="true" ColumnName="dt" DataType="double(255, 1)"> </DT>	
		<XZB Description="信噪比" Multiple="1" Function="" DataBegin="54" DataLength="2" HasSign="true" ColumnName="xzb" DataType="double(255, 2)"> </XZB>	
		<X Description="X" Multiple="0.01" Function="" DataBegin="56" DataLength="4" HasSign="true" ColumnName="X" DataType="double(255, 2)"> </X>
		<Y Description="Y" Multiple="0.01" Function="" DataBegin="60" DataLength="4" HasSign="true" ColumnName="Y" DataType="double(255, 2)"> </Y>
		<Z Description="Z" Multiple="0.01" Function="" DataBegin="64" DataLength="4" HasSign="true" ColumnName="Z" DataType="double(255, 2)"> </Z>
		<VX Description="VX" Multiple="0.001" Function="" DataBegin="68" DataLength="4" HasSign="true" ColumnName="VX" DataType="double(255, 3)"> </VX>
		<VY Description="VY" Multiple="0.001" Function="" DataBegin="72" DataLength="4" HasSign="true" ColumnName="VY" DataType="double(255, 3)"> </VY>
		<VZ Description="VZ" Multiple="0.001" Function="" DataBegin="76" DataLength="4" HasSign="true" ColumnName="VZ" DataType="double(255, 3)"> </VZ>
	</FrameStruct>
	<FrameStruct ID="11" Description="KAVRFirst" FrameLength="32" DataTableName="out_ka_speed_table_first">
		<T Description="当前时刻" Multiple="1" Function="" DataBegin="32" DataLength="4" HasSign="false" ColumnName="t" DataType="int(11)"> </T>
		<ZT Description="设备状态" Multiple="1" Function="" DataBegin="36" DataLength="2" HasSign="false" ColumnName="zt" DataType="int(11)"> </ZT>	
		<RR Description="速度" Multiple="1" Function="" DataBegin="38" DataLength="4" HasSign="true" ColumnName="rr" DataType="double(255, 2)"> </RR>	
		<DS Description="弹地频差" Multiple="1" Function="" DataBegin="42" DataLength="4" HasSign="true" ColumnName="ds" DataType="double(255, 2)"> </DS>	
		<XZB Description="信噪比" Multiple="1" Function="" DataBegin="46" DataLength="2" HasSign="true" ColumnName="xzb" DataType="double(255, 2)"> </XZB>	
		<X Description="X" Multiple="0.01" Function="" DataBegin="48" DataLength="4" HasSign="true" ColumnName="X" DataType="double(255, 2)"> </X>
		<Y Description="Y" Multiple="0.01" Function="" DataBegin="52" DataLength="4" HasSign="true" ColumnName="Y" DataType="double(255, 2)"> </Y>
		<Z Description="Z" Multiple="0.01" Function="" DataBegin="56" DataLength="4" HasSign="true" ColumnName="Z" DataType="double(255, 2)"> </Z>
		<VX Description="VX" Multiple="0.001" Function="" DataBegin="60" DataLength="4" HasSign="true" ColumnName="VX" DataType="double(255, 3)"> </VX>
		<VY Description="VY" Multiple="0.001" Function="" DataBegin="64" DataLength="4" HasSign="true" ColumnName="VY" DataType="double(255, 3)"> </VY>
		<VZ Description="VZ" Multiple="0.001" Function="" DataBegin="68" DataLength="4" HasSign="true" ColumnName="VZ" DataType="double(255, 3)"> </VZ>
	</FrameStruct>
	<FrameStruct ID="12" Description="KAVRSecond" FrameLength="32" DataTableName="out_ka_speed_table_second">
		<T Description="当前时刻" Multiple="1" Function="" DataBegin="32" DataLength="4" HasSign="false" ColumnName="t" DataType="int(11)"> </T>
		<ZT Description="设备状态" Multiple="1" Function="" DataBegin="36" DataLength="2" HasSign="false" ColumnName="zt" DataType="int(11)"> </ZT>	
		<RR Description="速度" Multiple="1" Function="" DataBegin="38" DataLength="4" HasSign="true" ColumnName="rr" DataType="double(255, 2)"> </RR>	
		<DS Description="弹地频差" Multiple="1" Function="" DataBegin="42" DataLength="4" HasSign="true" ColumnName="ds" DataType="double(255, 2)"> </DS>	
		<XZB Description="信噪比" Multiple="1" Function="" DataBegin="46" DataLength="2" HasSign="true" ColumnName="xzb" DataType="double(255, 2)"> </XZB>	
		<X Description="X" Multiple="0.01" Function="" DataBegin="48" DataLength="4" HasSign="true" ColumnName="X" DataType="double(255, 2)"> </X>
		<Y Description="Y" Multiple="0.01" Function="" DataBegin="52" DataLength="4" HasSign="true" ColumnName="Y" DataType="double(255, 2)"> </Y>
		<Z Description="Z" Multiple="0.01" Function="" DataBegin="56" DataLength="4" HasSign="true" ColumnName="Z" DataType="double(255, 2)"> </Z>
		<VX Description="VX" Multiple="0.001" Function="" DataBegin="60" DataLength="4" HasSign="true" ColumnName="VX" DataType="double(255, 3)"> </VX>
		<VY Description="VY" Multiple="0.001" Function="" DataBegin="64" DataLength="4" HasSign="true" ColumnName="VY" DataType="double(255, 3)"> </VY>
		<VZ Description="VZ" Multiple="0.001" Function="" DataBegin="68" DataLength="4" HasSign="true" ColumnName="VZ" DataType="double(255, 3)"> </VZ>
	</FrameStruct>
	<FrameStruct ID="13" Description="ZKTFirst" FrameLength="32" DataTableName="out_zkt_table_first">
		<T Description="当前时刻" Multiple="1" Function="" DataBegin="32" DataLength="4" HasSign="false" ColumnName="t" DataType="int(11)"> </T>
		<X Description="X" Multiple="1" Function="" DataBegin="36" DataLength="4" HasSign="true" ColumnName="x" DataType="double(255, 1)"> </X>	
		<Y Description="Y" Multiple="1" Function="" DataBegin="40" DataLength="4" HasSign="true" ColumnName="y" DataType="double(255, 1)"> </Y>	
		<Z Description="Z" Multiple="1" Function="" DataBegin="44" DataLength="4" HasSign="true" ColumnName="z" DataType="double(255, 1)"> </Z>	
		<VX Description="VX" Multiple="1" Function="" DataBegin="48" DataLength="4" HasSign="true" ColumnName="vx" DataType="double(255, 2)"> </VX>	
		<VY Description="VY" Multiple="1" Function="" DataBegin="52" DataLength="4" HasSign="true" ColumnName="vy" DataType="double(255, 2)"> </VY>	
		<VZ Description="VZ" Multiple="1" Function="" DataBegin="56" DataLength="4" HasSign="true" ColumnName="vz" DataType="double(255, 2)"> </VZ>	
	</FrameStruct>
	<FrameStruct ID="14" Description="ZKTSecond" FrameLength="32" DataTableName="out_zkt_table_second">
		<T Description="当前时刻" Multiple="1" Function="" DataBegin="32" DataLength="4" HasSign="false" ColumnName="t" DataType="int(11)"> </T>
		<X Description="X" Multiple="1" Function="" DataBegin="36" DataLength="4" HasSign="true" ColumnName="x" DataType="double(255, 1)"> </X>	
		<Y Description="Y" Multiple="1" Function="" DataBegin="40" DataLength="4" HasSign="true" ColumnName="y" DataType="double(255, 1)"> </Y>	
		<Z Description="Z" Multiple="1" Function="" DataBegin="44" DataLength="4" HasSign="true" ColumnName="z" DataType="double(255, 1)"> </Z>	
		<VX Description="VX" Multiple="1" Function="" DataBegin="48" DataLength="4" HasSign="true" ColumnName="vx" DataType="double(255, 2)"> </VX>	
		<VY Description="VY" Multiple="1" Function="" DataBegin="52" DataLength="4" HasSign="true" ColumnName="vy" DataType="double(255, 2)"> </VY>	
		<VZ Description="VZ" Multiple="1" Function="" DataBegin="56" DataLength="4" HasSign="true" ColumnName="vz" DataType="double(255, 2)"> </VZ>	
	</FrameStruct>
	<FrameStruct ID="15" Description="XLMCLDDevice" FrameLength="32" DataTableName="xl_mcld_table">
		<N Description="目标数" Multiple="1" Function="" DataBegin="32" DataLength="1" HasSign="false" ColumnName="n" DataType="int(11)"> </N>
		<MB1 Description="标志码" Multiple="1" Function="" DataBegin="33" DataLength="1" HasSign="false" ColumnName="mb1" DataType="int(11)"> </MB1>
		<T Description="当前时刻" Multiple="1" Function="" DataBegin="34" DataLength="4" HasSign="false" ColumnName="t" DataType="int(11)"> </T>
		<ZT Description="设备状态" Multiple="1" Function="" DataBegin="38" DataLength="1" HasSign="false" ColumnName="zt" DataType="int(11)"> </ZT>	
		<V Description="测速值" Multiple="1" Function="" DataBegin="39" DataLength="4" HasSign="true" ColumnName="v" DataType="double(255, 2)"> </V>	
		<R Description="径向距离值" Multiple="1" Function="" DataBegin="43" DataLength="4" HasSign="true" ColumnName="r" DataType="double(255, 2)"> </R>	
		<A Description="方位角" Multiple="0.000000168" Function="" DataBegin="47" DataLength="4" HasSign="false" ColumnName="a" DataType="double(255, 9)"> </A>	
		<E Description="俯仰角" Multiple="0.000000168" Function="" DataBegin="51" DataLength="4" HasSign="false" ColumnName="e" DataType="double(255, 9)"> </E>	
		<DA Description="方位角动态滞后修正量" Multiple="1" Function="" DataBegin="55" DataLength="1" HasSign="true" ColumnName="da" DataType="double(255, 2)"> </DA>	
		<DE Description="俯仰角动态滞后修正量" Multiple="1" Function="" DataBegin="56" DataLength="1" HasSign="true" ColumnName="de" DataType="double(255, 2)"> </DE>	
		<AGC Description="接收机自动增益控制量" Multiple="1" Function="" DataBegin="57" DataLength="1" HasSign="true" ColumnName="agc" DataType="double(255, 2)"> </AGC>	
		<RCS Description="目标的窄带RCS幅度值" Multiple="1" Function="" DataBegin="58" DataLength="1" HasSign="true" ColumnName="rcs" DataType="double(255, 2)"> </RCS>
		<X Description="X" Multiple="0.01" Function="" DataBegin="59" DataLength="4" HasSign="true" ColumnName="X" DataType="double(255, 2)"> </X>
		<Y Description="Y" Multiple="0.01" Function="" DataBegin="63" DataLength="4" HasSign="true" ColumnName="Y" DataType="double(255, 2)"> </Y>
		<Z Description="Z" Multiple="0.01" Function="" DataBegin="67" DataLength="4" HasSign="true" ColumnName="Z" DataType="double(255, 2)"> </Z>
		<VX Description="VX" Multiple="0.001" Function="" DataBegin="71" DataLength="4" HasSign="true" ColumnName="VX" DataType="double(255, 3)"> </VX>
		<VY Description="VY" Multiple="0.001" Function="" DataBegin="75" DataLength="4" HasSign="true" ColumnName="VY" DataType="double(255, 3)"> </VY>
		<VZ Description="VZ" Multiple="0.001" Function="" DataBegin="79" DataLength="4" HasSign="true" ColumnName="VZ" DataType="double(255, 3)"> </VZ>
	</FrameStruct>
	<FrameStruct ID="16" Description="起飞信号" FrameLength="32" DataTableName="take_off_signal_frame_first">		
		<T0 Description="t0" Multiple="1" Function="" DataBegin="32" DataLength="4" HasSign="false" ColumnName="t0" DataType="int(11)"> </T0>			
	</FrameStruct>
	<FrameStruct ID="17" Description="基地间数据" FrameLength="32" DataTableName="jdj_frame">		
		<T Description="T" Multiple="1" Function="" DataBegin="32" DataLength="4" HasSign="false" ColumnName="t" DataType="int(11)"> </T>			
	</FrameStruct>
	<FrameStruct ID="18" Description="Ka控制命令状态" FrameLength="32" DataTableName="ka_control_status_frame">		
		<T Description="T" Multiple="1" Function="" DataBegin="32" DataLength="4" HasSign="false" ColumnName="t" DataType="int(11)"> </T>			
	</FrameStruct>
	<FrameStruct ID="19" Description="遥测原始" FrameLength="32" DataTableName="remote_original_frame">
		<T Description="ZT" Multiple="1" Function="" DataBegin="32" DataLength="2" HasSign="false" ColumnName="zt" DataType="int(11)"> </T>	
		<T Description="ZS" Multiple="1" Function="" DataBegin="34" DataLength="2" HasSign="false" ColumnName="zs" DataType="int(11)"> </T>					
	</FrameStruct>
	<FrameStruct ID="20" Description="机动测站" FrameLength="32" DataTableName="mobile_address">
		<T Description="T" Multiple="1" Function="" DataBegin="32" DataLength="4" HasSign="false" ColumnName="t" DataType="int(11)"> </T>	
		<X Description="X" Multiple="0.01" Function="" DataBegin="36" DataLength="4" HasSign="true" ColumnName="x" DataType="double(255, 2)"> </X>
		<Y Description="Y" Multiple="0.01" Function="" DataBegin="40" DataLength="4" HasSign="true" ColumnName="y" DataType="double(255, 2)"> </Y>
		<Z Description="Z" Multiple="0.01" Function="" DataBegin="44" DataLength="4" HasSign="true" ColumnName="z" DataType="double(255, 2)"> </Z>
		<VX Description="VX" Multiple="0.001" Function="" DataBegin="48" DataLength="4" HasSign="true" ColumnName="vx" DataType="double(255, 3)"> </VX>
		<VY Description="VY" Multiple="0.001" Function="" DataBegin="52" DataLength="4" HasSign="true" ColumnName="vy" DataType="double(255, 3)"> </VY>
		<VZ Description="VZ" Multiple="0.001" Function="" DataBegin="56" DataLength="4" HasSign="true" ColumnName="vz" DataType="double(255, 3)"> </VZ>	
	</FrameStruct>
	<FrameStruct ID="21" Description="track" FrameLength="32" DataTableName="satallite_track_address">		
		<T Description="T" Multiple="1" Function="" DataBegin="38" DataLength="4" HasSign="false" ColumnName="t" DataType="int(11)"> </T>	
		<X Description="L" Multiple="1" Function="" DataBegin="42" DataLength="8" HasSign="true" ColumnName="l" DataType="double(255, 2)"> </X>
		<Y Description="B" Multiple="1" Function="" DataBegin="50" DataLength="8" HasSign="true" ColumnName="b" DataType="double(255, 2)"> </Y>
		<Z Description="H" Multiple="1" Function="" DataBegin="58" DataLength="4" HasSign="true" ColumnName="h" DataType="double(255, 2)"> </Z>
	</FrameStruct>
	<FrameStruct ID="100" Description="主平面链监" FrameLength="32" DataTableName="device_lj_table_first">		
		<LJT Description="LJT" Multiple="1" Function="" DataBegin="32" DataLength="4" HasSign="false" ColumnName="lj_t" DataType="int(11)"> </LJT>			
	</FrameStruct>
	<FrameStruct ID="101" Description="二平面链监" FrameLength="32" DataTableName="device_lj_table_second">		
		<LJT Description="LJT" Multiple="1" Function="" DataBegin="32" DataLength="4" HasSign="false" ColumnName="lj_t" DataType="int(11)"> </LJT>			
	</FrameStruct>
	<FrameStruct ID="110" Description="统计信息" FrameLength="32" DataTableName="data_receive_statistic_frame">		
		<MBR Description="MBR" Multiple="1" Function="" DataBegin="32" DataLength="1" HasSign="false" ColumnName="mbr" DataType="int(11)"> </MBR>	
		<RN Description="RN" Multiple="1" Function="" DataBegin="33" DataLength="4" HasSign="false" ColumnName="rn" DataType="int(11)"> </RN>					
	</FrameStruct>
	<FrameStruct ID="130" Description="主平面方案弹道帧" FrameLength="153" DataTableName="scheme_traj_frame_first">
		<TrajDate Description="弹道日期" Multiple="1" Function="" DataBegin="32" DataLength="2" HasSign="false" ColumnName="traj_date" DataType="int(11)"> </TrajDate>
		<TrajTime Description="弹道时标" Multiple="1" Function="" DataBegin="34" DataLength="4" HasSign="false" ColumnName="traj_t" DataType="int(11)"> </TrajTime>		
		<PN Description="方案号" Multiple="1" Function="" DataBegin="38" DataLength="2" HasSign="false" ColumnName="traj_pn" DataType="int(11)"> </PN>		
		<SelectFlag Description="选中标志" Multiple="1" Function="" DataBegin="40" DataLength="2" HasSign="false" ColumnName="traj_flag" DataType="int(11)"> </SelectFlag>	
		<LaunchX Description="发射系弹道X" Multiple="0.1" Function="" DataBegin="42" DataLength="4" HasSign="true" ColumnName="launch_x" DataType="double(255, 1)"> </LaunchX>	
		<LaunchY Description="发射系弹道Y" Multiple="0.1" Function="" DataBegin="46" DataLength="4" HasSign="true" ColumnName="launch_y" DataType="double(255, 1)"> </LaunchY>			
		<LaunchZ Description="发射系弹道Z" Multiple="0.1" Function="" DataBegin="50" DataLength="4" HasSign="true" ColumnName="launch_z" DataType="double(255, 1)"> </LaunchZ>	
		<LaunchVX Description="发射系弹道VX" Multiple="0.01" Function="" DataBegin="54" DataLength="4" HasSign="true" ColumnName="launch_vx" DataType="double(255, 2)"> </LaunchVX>	
		<LaunchVY Description="发射系弹道VY" Multiple="0.01" Function="" DataBegin="58" DataLength="4" HasSign="true" ColumnName="launch_vy" DataType="double(255, 2)"> </LaunchVY>	
		<LaunchVZ Description="发射系弹道VZ" Multiple="0.01" Function="" DataBegin="62" DataLength="4" HasSign="true" ColumnName="launch_vz" DataType="double(255, 2)"> </LaunchVZ>	
		<LaunchV Description="发射系弹道V" Multiple="0.01" Function="" DataBegin="66" DataLength="4" HasSign="true" ColumnName="launch_v" DataType="double(255, 2)"> </LaunchV>	
		<RocketLambda Description="箭下点经度" Multiple="0.0000002384185791015625" Function="" DataBegin="70" DataLength="4" HasSign="true" ColumnName="rocket_lambda" DataType="double(255, 11)"> </RocketLambda>	
		<RocketPsi Description="箭下点纬度" Multiple="0.0000002384185791015625" Function="" DataBegin="74" DataLength="4" HasSign="true" ColumnName="rocket_psi" DataType="double(255, 11)"> </RocketPsi>	
		<RocketTheta Description="箭下点倾角" Multiple="0.01" Function="" DataBegin="78" DataLength="4" HasSign="true" ColumnName="rocket_theta" DataType="double(255, 2)"> </RocketTheta>	
		<RocketSigma Description="箭下点偏角" Multiple="0.01" Function="" DataBegin="82" DataLength="4" HasSign="true" ColumnName="rocket_sigma" DataType="double(255, 2)"> </RocketSigma>	
		<RocketH Description="箭下点高度" Multiple="0.1" Function="" DataBegin="86" DataLength="4" HasSign="true" ColumnName="rocket_h" DataType="double(255, 1)"> </RocketH>	
		<RocketLx Description="箭下点航程Lx" Multiple="0.1" Function="" DataBegin="90" DataLength="4" HasSign="true" ColumnName="rocket_lx" DataType="double(255, 1)"> </RocketLx>	
		<RocketLz Description="箭下点航程Lz" Multiple="0.1" Function="" DataBegin="94" DataLength="4" HasSign="true" ColumnName="rocket_lz" DataType="double(255, 1)"> </RocketLz>
		<CoreX Description="地心系弹道X" Multiple="0.1" Function="" DataBegin="102" DataLength="4" HasSign="true" ColumnName="core_x" DataType="double(255, 1)"> </CoreX>	
		<CoreY Description="地心系弹道Y" Multiple="0.1" Function="" DataBegin="106" DataLength="4" HasSign="true" ColumnName="core_y" DataType="double(255, 1)"> </CoreY>			
		<CoreZ Description="地心系弹道Z" Multiple="0.1" Function="" DataBegin="110" DataLength="4" HasSign="true" ColumnName="core_z" DataType="double(255, 1)"> </CoreZ>	
		<CoreVX Description="地心系弹道VX" Multiple="0.01" Function="" DataBegin="114" DataLength="4" HasSign="true" ColumnName="core_vx" DataType="double(255, 2)"> </CoreVX>	
		<CoreVY Description="地心系弹道VY" Multiple="0.01" Function="" DataBegin="118" DataLength="4" HasSign="true" ColumnName="core_vy" DataType="double(255, 2)"> </CoreVY>	
		<CoreVZ Description="地心系弹道VZ" Multiple="0.01" Function="" DataBegin="122" DataLength="4" HasSign="true" ColumnName="core_vz" DataType="double(255, 2)"> </CoreVZ>	
		<LR Description="航程LR" Multiple="0.1" Function="" DataBegin="126" DataLength="4" HasSign="true" ColumnName="lr" DataType="double(255, 1)"> </LR>
		<LX Description="纵向距离LX" Multiple="0.1" Function="" DataBegin="130" DataLength="4" HasSign="true" ColumnName="lx" DataType="double(255, 1)"> </LX>	
		<LZ Description="横向距离LZ" Multiple="0.1" Function="" DataBegin="134" DataLength="4" HasSign="true" ColumnName="lz" DataType="double(255, 1)"> </LZ>			
		<DLX Description="纵向偏差DLX" Multiple="0.1" Function="" DataBegin="138" DataLength="4" HasSign="true" ColumnName="dlx" DataType="double(255, 1)"> </DLX>	
		<DLZ Description="横向偏差DLZ" Multiple="0.1" Function="" DataBegin="142" DataLength="4" HasSign="true" ColumnName="dlz" DataType="double(255, 1)"> </DLZ>	
		<L Description="大地经度L" Multiple="0.0000002384185791015625" Function="" DataBegin="146" DataLength="4" HasSign="true" ColumnName="l" DataType="double(255, 11)"> </L>	
		<B Description="大地纬度B" Multiple="0.0000002384185791015625" Function="" DataBegin="150" DataLength="4" HasSign="true" ColumnName="b" DataType="double(255, 11)"> </B>	
	</FrameStruct>
	<FrameStruct ID="131" Description="二平面方案弹道帧" FrameLength="153" DataTableName="scheme_traj_frame_second">
		<TrajDate Description="弹道日期" Multiple="1" Function="" DataBegin="32" DataLength="2" HasSign="false" ColumnName="traj_date" DataType="int(11)"> </TrajDate>
		<TrajTime Description="弹道时标" Multiple="1" Function="" DataBegin="34" DataLength="4" HasSign="false" ColumnName="traj_t" DataType="int(11)"> </TrajTime>		
		<PN Description="方案号" Multiple="1" Function="" DataBegin="38" DataLength="2" HasSign="false" ColumnName="traj_pn" DataType="int(11)"> </PN>		
		<SelectFlag Description="选中标志" Multiple="1" Function="" DataBegin="40" DataLength="2" HasSign="false" ColumnName="traj_flag" DataType="int(11)"> </SelectFlag>	
		<LaunchX Description="发射系弹道X" Multiple="0.1" Function="" DataBegin="42" DataLength="4" HasSign="true" ColumnName="launch_x" DataType="double(255, 1)"> </LaunchX>	
		<LaunchY Description="发射系弹道Y" Multiple="0.1" Function="" DataBegin="46" DataLength="4" HasSign="true" ColumnName="launch_y" DataType="double(255, 1)"> </LaunchY>			
		<LaunchZ Description="发射系弹道Z" Multiple="0.1" Function="" DataBegin="50" DataLength="4" HasSign="true" ColumnName="launch_z" DataType="double(255, 1)"> </LaunchZ>	
		<LaunchVX Description="发射系弹道VX" Multiple="0.01" Function="" DataBegin="54" DataLength="4" HasSign="true" ColumnName="launch_vx" DataType="double(255, 2)"> </LaunchVX>	
		<LaunchVY Description="发射系弹道VY" Multiple="0.01" Function="" DataBegin="58" DataLength="4" HasSign="true" ColumnName="launch_vy" DataType="double(255, 2)"> </LaunchVY>	
		<LaunchVZ Description="发射系弹道VZ" Multiple="0.01" Function="" DataBegin="62" DataLength="4" HasSign="true" ColumnName="launch_vz" DataType="double(255, 2)"> </LaunchVZ>	
		<LaunchV Description="发射系弹道V" Multiple="0.01" Function="" DataBegin="66" DataLength="4" HasSign="true" ColumnName="launch_v" DataType="double(255, 2)"> </LaunchV>	
		<RocketLambda Description="箭下点经度" Multiple="0.0000002384185791015625" Function="" DataBegin="70" DataLength="4" HasSign="true" ColumnName="rocket_lambda" DataType="double(255, 11)"> </RocketLambda>	
		<RocketPsi Description="箭下点纬度" Multiple="0.0000002384185791015625" Function="" DataBegin="74" DataLength="4" HasSign="true" ColumnName="rocket_psi" DataType="double(255, 11)"> </RocketPsi>	
		<RocketTheta Description="箭下点倾角" Multiple="0.01" Function="" DataBegin="78" DataLength="4" HasSign="true" ColumnName="rocket_theta" DataType="double(255, 2)"> </RocketTheta>	
		<RocketSigma Description="箭下点偏角" Multiple="0.01" Function="" DataBegin="82" DataLength="4" HasSign="true" ColumnName="rocket_sigma" DataType="double(255, 2)"> </RocketSigma>	
		<RocketH Description="箭下点高度" Multiple="0.1" Function="" DataBegin="86" DataLength="4" HasSign="true" ColumnName="rocket_h" DataType="double(255, 1)"> </RocketH>	
		<RocketLx Description="箭下点航程Lx" Multiple="0.1" Function="" DataBegin="90" DataLength="4" HasSign="true" ColumnName="rocket_lx" DataType="double(255, 1)"> </RocketLx>	
		<RocketLz Description="箭下点航程Lz" Multiple="0.1" Function="" DataBegin="94" DataLength="4" HasSign="true" ColumnName="rocket_lz" DataType="double(255, 1)"> </RocketLz>
		<CoreX Description="地心系弹道X" Multiple="0.1" Function="" DataBegin="102" DataLength="4" HasSign="true" ColumnName="core_x" DataType="double(255, 1)"> </CoreX>	
		<CoreY Description="地心系弹道Y" Multiple="0.1" Function="" DataBegin="106" DataLength="4" HasSign="true" ColumnName="core_y" DataType="double(255, 1)"> </CoreY>			
		<CoreZ Description="地心系弹道Z" Multiple="0.1" Function="" DataBegin="110" DataLength="4" HasSign="true" ColumnName="core_z" DataType="double(255, 1)"> </CoreZ>	
		<CoreVX Description="地心系弹道VX" Multiple="0.01" Function="" DataBegin="114" DataLength="4" HasSign="true" ColumnName="core_vx" DataType="double(255, 2)"> </CoreVX>	
		<CoreVY Description="地心系弹道VY" Multiple="0.01" Function="" DataBegin="118" DataLength="4" HasSign="true" ColumnName="core_vy" DataType="double(255, 2)"> </CoreVY>	
		<CoreVZ Description="地心系弹道VZ" Multiple="0.01" Function="" DataBegin="122" DataLength="4" HasSign="true" ColumnName="core_vz" DataType="double(255, 2)"> </CoreVZ>	
		<LR Description="航程LR" Multiple="0.1" Function="" DataBegin="126" DataLength="4" HasSign="true" ColumnName="lr" DataType="double(255, 1)"> </LR>
		<LX Description="纵向距离LX" Multiple="0.1" Function="" DataBegin="130" DataLength="4" HasSign="true" ColumnName="lx" DataType="double(255, 1)"> </LX>	
		<LZ Description="横向距离LZ" Multiple="0.1" Function="" DataBegin="134" DataLength="4" HasSign="true" ColumnName="lz" DataType="double(255, 1)"> </LZ>			
		<DLX Description="纵向偏差DLX" Multiple="0.1" Function="" DataBegin="138" DataLength="4" HasSign="true" ColumnName="dlx" DataType="double(255, 1)"> </DLX>	
		<DLZ Description="横向偏差DLZ" Multiple="0.1" Function="" DataBegin="142" DataLength="4" HasSign="true" ColumnName="dlz" DataType="double(255, 1)"> </DLZ>	
		<L Description="大地经度L" Multiple="0.0000002384185791015625" Function="" DataBegin="146" DataLength="4" HasSign="true" ColumnName="l" DataType="double(255, 11)"> </L>	
		<B Description="大地纬度B" Multiple="0.0000002384185791015625" Function="" DataBegin="150" DataLength="4" HasSign="true" ColumnName="b" DataType="double(255, 11)"> </B>		
	</FrameStruct>
	<FrameStruct ID="132" Description="主平面偏差弹道帧" FrameLength="32" DataTableName="difference_traj_frame_first">
		<TrajDate Description="弹道日期" Multiple="1" Function="" DataBegin="32" DataLength="2" HasSign="false" ColumnName="traj_date" DataType="int(11)"> </TrajDate>
		<TrajTime Description="弹道时标" Multiple="1" Function="" DataBegin="34" DataLength="4" HasSign="false" ColumnName="traj_t" DataType="int(11)"> </TrajTime>		
		<PN Description="方案号" Multiple="1" Function="" DataBegin="38" DataLength="2" HasSign="false" ColumnName="traj_pn" DataType="int(11)"> </PN>		
		<DX Description="发射系弹道X" Multiple="0.1" Function="" DataBegin="40" DataLength="4" HasSign="true" ColumnName="dx" DataType="double(255, 1)"> </DX>	
		<DY Description="发射系弹道Y" Multiple="0.1" Function="" DataBegin="44" DataLength="4" HasSign="true" ColumnName="dy" DataType="double(255, 1)"> </DY>			
		<DZ Description="发射系弹道Z" Multiple="0.1" Function="" DataBegin="48" DataLength="4" HasSign="true" ColumnName="dz" DataType="double(255, 1)"> </DZ>	
		<DVX Description="发射系弹道VX" Multiple="0.01" Function="" DataBegin="52" DataLength="4" HasSign="true" ColumnName="dvx" DataType="double(255, 2)"> </DVX>	
		<DVY Description="发射系弹道VY" Multiple="0.01" Function="" DataBegin="56" DataLength="4" HasSign="true" ColumnName="dvy" DataType="double(255, 2)"> </DVY>	
		<DVZ Description="发射系弹道VZ" Multiple="0.01" Function="" DataBegin="60" DataLength="4" HasSign="true" ColumnName="dvz" DataType="double(255, 2)"> </DVZ>	
		<AX Description="AX" Multiple="0.01" Function="" DataBegin="64" DataLength="4" HasSign="true" ColumnName="ax" DataType="double(255, 2)"> </AX>	
		<AY Description="AY" Multiple="0.01" Function="" DataBegin="68" DataLength="4" HasSign="true" ColumnName="ay" DataType="double(255, 2)"> </AY>	
		<AZ Description="AZ" Multiple="0.01" Function="" DataBegin="72" DataLength="4" HasSign="true" ColumnName="az" DataType="double(255, 2)"> </AZ>	
	</FrameStruct>
	<FrameStruct ID="133" Description="二平面偏差弹道帧" FrameLength="32" DataTableName="difference_traj_frame_second">
		<TrajDate Description="弹道日期" Multiple="1" Function="" DataBegin="32" DataLength="2" HasSign="false" ColumnName="traj_date" DataType="int(11)"> </TrajDate>
		<TrajTime Description="弹道时标" Multiple="1" Function="" DataBegin="34" DataLength="4" HasSign="false" ColumnName="traj_t" DataType="int(11)"> </TrajTime>		
		<PN Description="方案号" Multiple="1" Function="" DataBegin="38" DataLength="2" HasSign="false" ColumnName="traj_pn" DataType="int(11)"> </PN>		
		<DX Description="发射系弹道X" Multiple="0.1" Function="" DataBegin="40" DataLength="4" HasSign="true" ColumnName="dx" DataType="double(255, 1)"> </DX>	
		<DY Description="发射系弹道Y" Multiple="0.1" Function="" DataBegin="44" DataLength="4" HasSign="true" ColumnName="dy" DataType="double(255, 1)"> </DY>			
		<DZ Description="发射系弹道Z" Multiple="0.1" Function="" DataBegin="48" DataLength="4" HasSign="true" ColumnName="dz" DataType="double(255, 1)"> </DZ>	
		<DVX Description="发射系弹道VX" Multiple="0.01" Function="" DataBegin="52" DataLength="4" HasSign="true" ColumnName="dvx" DataType="double(255, 2)"> </DVX>	
		<DVY Description="发射系弹道VY" Multiple="0.01" Function="" DataBegin="56" DataLength="4" HasSign="true" ColumnName="dvy" DataType="double(255, 2)"> </DVY>	
		<DVZ Description="发射系弹道VZ" Multiple="0.01" Function="" DataBegin="60" DataLength="4" HasSign="true" ColumnName="dvz" DataType="double(255, 2)"> </DVZ>	
		<AX Description="AX" Multiple="0.01" Function="" DataBegin="64" DataLength="4" HasSign="true" ColumnName="ax" DataType="double(255, 2)"> </AX>	
		<AY Description="AY" Multiple="0.01" Function="" DataBegin="68" DataLength="4" HasSign="true" ColumnName="ay" DataType="double(255, 2)"> </AY>	
		<AZ Description="AZ" Multiple="0.01" Function="" DataBegin="72" DataLength="4" HasSign="true" ColumnName="az" DataType="double(255, 2)"> </AZ>	
	</FrameStruct>

	<FrameStruct ID="134" Description="一平面卫星轨道根数帧" FrameLength="32" DataTableName="satellite_orbit_frame_first">
		<Num Description="目标编号" Multiple="1" Function="" DataBegin="32" DataLength="1" HasSign="false" ColumnName="num" DataType="int(11)"> </Num>
		<PN Description="方案号" Multiple="1" Function="" DataBegin="33" DataLength="2" HasSign="false" ColumnName="traj_pn" DataType="int(11)"> </PN>
		<Reserve Description="保留子段" Multiple="1" Function="" DataBegin="35" DataLength="2" HasSign="false" ColumnName="Reserve" DataType="int(11)"> </Reserve>		
		<BJTime1 Description="历元时刻1" Multiple="1" Function="" DataBegin="37" DataLength="4" HasSign="false" ColumnName="bj_time1" DataType="int(11)"> </BJTime1>	
		<BJTime2 Description="历元时刻2" Multiple="0.1" Function="" DataBegin="41" DataLength="4" HasSign="false" ColumnName="bj_time2" DataType="double(255, 1)"> </BJTime2>
		<A Description="轨道半长轴" Multiple="0.1" Function="" DataBegin="45" DataLength="4" HasSign="true" ColumnName="a" DataType="double(255, 1)"> </A>	
		<E Description="轨道偏心率" Multiple="0.0000000004656612873077392578125" Function="" DataBegin="49" DataLength="4" HasSign="true" ColumnName="e" DataType="double(255, 30)"> </E>
		<I Description="轨道倾角" Multiple="0.0000002384185791015625" Function="" DataBegin="53" DataLength="4" HasSign="true" ColumnName="i" DataType="double(255, 22)"> </I>
		<O Description="升交点赤经" Multiple="0.0000002384185791015625" Function="" DataBegin="57" DataLength="4" HasSign="false" ColumnName="o" DataType="double(255, 22)"> </O>
		<W Description="近地点幅角" Multiple="0.0000002384185791015625" Function="" DataBegin="61" DataLength="4" HasSign="true" ColumnName="w" DataType="double(255, 22)"> </W>
		<M Description="平近点角" Multiple="0.0000002384185791015625" Function="" DataBegin="65" DataLength="4" HasSign="true" ColumnName="m" DataType="double(255, 22)"> </M>
		<P Description="轨道周期" Multiple="0.00000095367431640625" Function="" DataBegin="69" DataLength="4" HasSign="true" ColumnName="p" DataType="double(255, 20)"> </P>
		<Dp Description="轨道周期变化率" Multiple="0.00000095367431640625" Function="" DataBegin="73" DataLength="4" HasSign="true" ColumnName="dp" DataType="double(255, 20)"> </Dp>
		<Ra Description="远地点地心距" Multiple="0.1" Function="" DataBegin="77" DataLength="4" HasSign="true" ColumnName="ra" DataType="double(255, 1)"> </Ra>
		<Rp Description="近地点地心距" Multiple="0.1" Function="" DataBegin="81" DataLength="4" HasSign="true" ColumnName="rp" DataType="double(255, 1)"> </Rp>
		<L Description="长度" Multiple="0.1" Function="" DataBegin="85" DataLength="1" HasSign="false" ColumnName="l" DataType="int(11)"> </L>
	</FrameStruct>

	<FrameStruct ID="135" Description="二平面卫星轨道根数帧" FrameLength="32" DataTableName="satellite_orbit_frame_second">
		<Num Description="目标编号" Multiple="1" Function="" DataBegin="32" DataLength="1" HasSign="false" ColumnName="num" DataType="int(11)"> </Num>
		<PN Description="方案号" Multiple="1" Function="" DataBegin="33" DataLength="2" HasSign="false" ColumnName="traj_pn" DataType="int(11)"> </PN>
		<Reserve Description="保留子段" Multiple="1" Function="" DataBegin="35" DataLength="2" HasSign="false" ColumnName="Reserve" DataType="int(11)"> </Reserve>		
		<BJTime1 Description="历元时刻1" Multiple="1" Function="" DataBegin="37" DataLength="4" HasSign="false" ColumnName="bj_time1" DataType="int(11)"> </BJTime1>	
		<BJTime2 Description="历元时刻2" Multiple="0.1" Function="" DataBegin="41" DataLength="4" HasSign="false" ColumnName="bj_time2" DataType="double(255, 1)"> </BJTime2>
		<A Description="轨道半长轴" Multiple="0.1" Function="" DataBegin="45" DataLength="4" HasSign="true" ColumnName="a" DataType="double(255, 1)"> </A>	
		<E Description="轨道偏心率" Multiple="0.0000000004656612873077392578125" Function="" DataBegin="49" DataLength="4" HasSign="true" ColumnName="e" DataType="double(255, 30)"> </E>
		<I Description="轨道倾角" Multiple="0.0000002384185791015625" Function="" DataBegin="53" DataLength="4" HasSign="true" ColumnName="i" DataType="double(255, 22)"> </I>
		<O Description="升交点赤经" Multiple="0.0000002384185791015625" Function="" DataBegin="57" DataLength="4" HasSign="false" ColumnName="o" DataType="double(255, 22)"> </O>
		<W Description="近地点幅角" Multiple="0.0000002384185791015625" Function="" DataBegin="61" DataLength="4" HasSign="true" ColumnName="w" DataType="double(255, 22)"> </W>
		<M Description="平近点角" Multiple="0.0000002384185791015625" Function="" DataBegin="65" DataLength="4" HasSign="true" ColumnName="m" DataType="double(255, 22)"> </M>
		<P Description="轨道周期" Multiple="0.00000095367431640625" Function="" DataBegin="69" DataLength="4" HasSign="true" ColumnName="p" DataType="double(255, 20)"> </P>
		<Dp Description="轨道周期变化率" Multiple="0.00000095367431640625" Function="" DataBegin="73" DataLength="4" HasSign="true" ColumnName="dp" DataType="double(255, 20)"> </Dp>
		<Ra Description="远地点地心距" Multiple="0.1" Function="" DataBegin="77" DataLength="4" HasSign="true" ColumnName="ra" DataType="double(255, 1)"> </Ra>
		<Rp Description="近地点地心距" Multiple="0.1" Function="" DataBegin="81" DataLength="4" HasSign="true" ColumnName="rp" DataType="double(255, 1)"> </Rp>
		<L Description="长度" Multiple="0.1" Function="" DataBegin="85" DataLength="1" HasSign="false" ColumnName="l" DataType="int(11)"> </L>
	</FrameStruct>

	<FrameStruct ID="140" Description="遥测原始弹道" FrameLength="32" DataTableName="remote_original_traj_frame">
		<TrajT Description="T" Multiple="1" Function="" DataBegin="32" DataLength="4" HasSign="false" ColumnName="traj_t" DataType="int(11)"> </TrajT>
		<ZT Description="ZT" Multiple="1" Function="" DataBegin="36" DataLength="1" HasSign="false" ColumnName="traj_zt" DataType="int(11)"> </ZT>
		<X Description="X" Multiple="0.1" Function="" DataBegin="37" DataLength="4" HasSign="true" ColumnName="x" DataType="double(255, 1)"> </X>	
		<Y Description="Y" Multiple="0.1" Function="" DataBegin="41" DataLength="4" HasSign="true" ColumnName="y" DataType="double(255, 1)"> </Y>	
		<Z Description="Z" Multiple="0.1" Function="" DataBegin="45" DataLength="4" HasSign="true" ColumnName="z" DataType="double(255, 1)"> </Z>	
		<VX Description="VX" Multiple="0.01" Function="" DataBegin="49" DataLength="4" HasSign="true" ColumnName="vx" DataType="double(255, 2)"> </VX>	
		<VY Description="VY" Multiple="0.01" Function="" DataBegin="53" DataLength="4" HasSign="true" ColumnName="vy" DataType="double(255, 2)"> </VY>	
		<VZ Description="VZ" Multiple="0.01" Function="" DataBegin="57" DataLength="4" HasSign="true" ColumnName="vz" DataType="double(255, 2)"> </VZ>	
	</FrameStruct>
	<FrameStruct ID="141" Description="遥测T0" FrameLength="32" DataTableName="remote_t0_frame">
		<CmdDate Description="CmdDate" Multiple="1" Function="" DataBegin="32" DataLength="2" HasSign="true" ColumnName="cmmd_date" DataType="int(11)"> </CmdDate>
		<T0 Description="T0" Multiple="1" Function="" DataBegin="36" DataLength="4" HasSign="false" ColumnName="cmmd_t0" DataType="int(11)"> </T0>
	</FrameStruct>
	<FrameStruct ID="142" Description="遥测参数" FrameLength="32" DataTableName="remote_parameter_frame">
		<ParaDate Description="ParaDate" Multiple="1" Function="" DataBegin="32" DataLength="2" HasSign="true" ColumnName="para_date" DataType="int(11)"> </ParaDate>
		<ParaRDev Description="ParaRDev" Multiple="1" Function="" DataBegin="34" DataLength="4" HasSign="false" ColumnName="para_rdev" DataType="int(11)"> </ParaRDev>
		<ParaReserved Description="ParaReserved" Multiple="1" Function="" DataBegin="38" DataLength="4" HasSign="false" ColumnName="para_reserved" DataType="int(11)"> </ParaReserved>
		<ParaN Description="ParaN" Multiple="1" Function="" DataBegin="42" DataLength="4" HasSign="false" ColumnName="para_n" DataType="int(11)"> </ParaN>
	</FrameStruct>
	<FrameStruct ID="143" Description="遥测设备状态" FrameLength="32" DataTableName="remote_device_status_frame">		
		<ZT Description="ZT" Multiple="1" Function="" DataBegin="32" DataLength="2" HasSign="false" ColumnName="zt" DataType="int(11)"> </ZT>			
	</FrameStruct>
	<FrameStruct ID="144" Description="遥测特征点弹道" FrameLength="32" DataTableName="remote_character_traj_frame">
		<TrajDate Description="TrajDate" Multiple="1" Function="" DataBegin="32" DataLength="2" HasSign="false" ColumnName="traj_date" DataType="int(11)"> </TrajDate>
		<TrajT Description="TrajT" Multiple="1" Function="" DataBegin="34" DataLength="4" HasSign="false" ColumnName="traj_t" DataType="int(11)"> </TrajT>
		<TrajPN Description="TrajPN" Multiple="1" Function="" DataBegin="38" DataLength="2" HasSign="false" ColumnName="traj_pn" DataType="int(11)"> </TrajPN>
		<TrajFlag Description="TrajFlag" Multiple="1" Function="" DataBegin="40" DataLength="2" HasSign="false" ColumnName="traj_flag" DataType="int(11)"> </TrajFlag>
		<TrajTK Description="TrajTK" Multiple="1" Function="" DataBegin="42" DataLength="4" HasSign="false" ColumnName="traj_tk" DataType="int(11)"> </TrajTK>
		<TrajBK Description="TrajBK" Multiple="1" Function="" DataBegin="46" DataLength="4" HasSign="false" ColumnName="traj_bk" DataType="int(11)"> </TrajBK>
		<X Description="X" Multiple="1" Function="" DataBegin="50" DataLength="4" HasSign="true" ColumnName="x" DataType="double(255, 1)"> </X>	
		<Y Description="Y" Multiple="1" Function="" DataBegin="54" DataLength="4" HasSign="true" ColumnName="y" DataType="double(255, 1)"> </Y>	
		<Z Description="Z" Multiple="1" Function="" DataBegin="58" DataLength="4" HasSign="true" ColumnName="z" DataType="double(255, 1)"> </Z>	
		<VX Description="VX" Multiple="1" Function="" DataBegin="62" DataLength="4" HasSign="true" ColumnName="vx" DataType="double(255, 2)"> </VX>	
		<VY Description="VY" Multiple="1" Function="" DataBegin="66" DataLength="4" HasSign="true" ColumnName="vy" DataType="double(255, 2)"> </VY>	
		<VZ Description="VZ" Multiple="1" Function="" DataBegin="70" DataLength="4" HasSign="true" ColumnName="vz" DataType="double(255, 2)"> </VZ>
		<EW Description="EW" Multiple="1" Function="" DataBegin="74" DataLength="4" HasSign="false" ColumnName="ew" DataType="int(11)"> </EW>
	</FrameStruct>
	<FrameStruct ID="145" Description="云平台输出的GNSS差分弹道" FrameLength="32" DataTableName="cloud_gnss_traj_frame">
		<TrajT Description="T" Multiple="1" Function="" DataBegin="32" DataLength="4" HasSign="false" ColumnName="traj_t" DataType="int(11)"> </TrajT>
		<ZT Description="ZT" Multiple="1" Function="" DataBegin="36" DataLength="1" HasSign="false" ColumnName="traj_zt" DataType="int(11)"> </ZT>
		<X Description="X" Multiple="1" Function="" DataBegin="37" DataLength="4" HasSign="true" ColumnName="x" DataType="double(255, 1)"> </X>	
		<Y Description="Y" Multiple="1" Function="" DataBegin="41" DataLength="4" HasSign="true" ColumnName="y" DataType="double(255, 1)"> </Y>	
		<Z Description="Z" Multiple="1" Function="" DataBegin="45" DataLength="4" HasSign="true" ColumnName="z" DataType="double(255, 1)"> </Z>	
		<VX Description="VX" Multiple="1" Function="" DataBegin="49" DataLength="4" HasSign="true" ColumnName="vx" DataType="double(255, 2)"> </VX>	
		<VY Description="VY" Multiple="1" Function="" DataBegin="53" DataLength="4" HasSign="true" ColumnName="vy" DataType="double(255, 2)"> </VY>	
		<VZ Description="VZ" Multiple="1" Function="" DataBegin="57" DataLength="4" HasSign="true" ColumnName="vz" DataType="double(255, 2)"> </VZ>	
	</FrameStruct>
	<FrameStruct ID="146" Description="引导弹道" FrameLength="32" DataTableName="inductive_traj_frame">
		<TrajT Description="T" Multiple="1" Function="" DataBegin="32" DataLength="4" HasSign="false" ColumnName="traj_t" DataType="int(11)"> </TrajT>
		<X Description="X" Multiple="1" Function="" DataBegin="36" DataLength="4" HasSign="true" ColumnName="x" DataType="double(255, 1)"> </X>	
		<Y Description="Y" Multiple="1" Function="" DataBegin="40" DataLength="4" HasSign="true" ColumnName="y" DataType="double(255, 1)"> </Y>	
		<Z Description="Z" Multiple="1" Function="" DataBegin="44" DataLength="4" HasSign="true" ColumnName="z" DataType="double(255, 1)"> </Z>	
		<VX Description="VX" Multiple="1" Function="" DataBegin="48" DataLength="4" HasSign="true" ColumnName="vx" DataType="double(255, 2)"> </VX>	
		<VY Description="VY" Multiple="1" Function="" DataBegin="52" DataLength="4" HasSign="true" ColumnName="vy" DataType="double(255, 2)"> </VY>	
		<VZ Description="VZ" Multiple="1" Function="" DataBegin="56" DataLength="4" HasSign="true" ColumnName="vz" DataType="double(255, 2)"> </VZ>	
	</FrameStruct>
	<FrameStruct ID="880" Description="GNSS基准信息" FrameLength="32" DataTableName="gnss_benchmark_data">
		<T Description="T" Multiple="1" Function="" DataBegin="32" DataLength="2" HasSign="false" ColumnName="t" DataType="int(11)"> </T>
	</FrameStruct>
	<FrameStruct ID="881" Description="GNSS原始信息" FrameLength="32" DataTableName="gnss_original_data">
		<T Description="T" Multiple="1" Function="" DataBegin="32" DataLength="2" HasSign="false" ColumnName="t" DataType="int(11)"> </T>
	</FrameStruct>
	<FrameStruct ID="882" Description="云平台GNSS原始信息" FrameLength="32" DataTableName="cloud_gnss_original_data">
		<T Description="T" Multiple="1" Function="" DataBegin="32" DataLength="2" HasSign="false" ColumnName="t" DataType="int(11)"> </T>
	</FrameStruct>
	<FrameStruct ID="883" Description="704输出的GNSS差分弹道" FrameLength="32" DataTableName="704_gnss_traj_frame">
		<TrajT Description="T" Multiple="1" Function="" DataBegin="32" DataLength="4" HasSign="false" ColumnName="traj_t" DataType="int(11)"> </TrajT>
		<ZT Description="ZT" Multiple="1" Function="" DataBegin="36" DataLength="1" HasSign="false" ColumnName="traj_zt" DataType="int(11)"> </ZT>
		<X Description="X" Multiple="1" Function="" DataBegin="37" DataLength="4" HasSign="true" ColumnName="x" DataType="double(255, 1)"> </X>	
		<Y Description="Y" Multiple="1" Function="" DataBegin="41" DataLength="4" HasSign="true" ColumnName="y" DataType="double(255, 1)"> </Y>	
		<Z Description="Z" Multiple="1" Function="" DataBegin="45" DataLength="4" HasSign="true" ColumnName="z" DataType="double(255, 1)"> </Z>	
		<VX Description="VX" Multiple="1" Function="" DataBegin="49" DataLength="4" HasSign="true" ColumnName="vx" DataType="double(255, 2)"> </VX>	
		<VY Description="VY" Multiple="1" Function="" DataBegin="53" DataLength="4" HasSign="true" ColumnName="vy" DataType="double(255, 2)"> </VY>	
		<VZ Description="VZ" Multiple="1" Function="" DataBegin="57" DataLength="4" HasSign="true" ColumnName="vz" DataType="double(255, 2)"> </VZ>	
	</FrameStruct>
	<FrameStruct ID="1001" Description="系统控制命令帧" FrameLength="32" DataTableName="system_control_command">		
		<CmdT Description="CmdT" Multiple="1" Function="" DataBegin="32" DataLength="4" HasSign="false" ColumnName="cmmd_t" DataType="int(11)"> </CmdT>	
		<CMD Description="CMD" Multiple="1" Function="" DataBegin="36" DataLength="2" HasSign="false" ColumnName="cmmd_mode" DataType="int(11)"> </CMD>
		<MODE Description="MODE" Multiple="1" Function="" DataBegin="38" DataLength="2" HasSign="false" ColumnName="cmmd_run_mode" DataType="int(11)"> </MODE>		
		<CmdDate Description="CmdDate" Multiple="1" Function="" DataBegin="40" DataLength="2" HasSign="true" ColumnName="cmmd_date" DataType="int(11)"> </CmdDate>
		<T0 Description="T0" Multiple="1" Function="" DataBegin="42" DataLength="4" HasSign="false" ColumnName="cmmd_t0" DataType="int(11)"> </T0>
		<CmdTrel Description="CmdTrel" Multiple="1" Function="" DataBegin="46" DataLength="4" HasSign="false" ColumnName="cmmd_trel" DataType="int(11)"> </CmdTrel>		
	</FrameStruct>
	<FrameStruct ID="1002" Description="进程控制命令帧" FrameLength="32" DataTableName="process_control_command">		
		<MODE Description="Reserved" Multiple="1" Function="" DataBegin="32" DataLength="2" HasSign="false" ColumnName="cmd_mode" DataType="int(11)"> </MODE>
		<ProNum Description="ProNum" Multiple="1" Function="" DataBegin="34" DataLength="4" HasSign="false" ColumnName="process_num" DataType="int(11)"> </ProNum>
	</FrameStruct>
	<FrameStruct ID="1003" Description="交互控制命令帧" FrameLength="32" DataTableName="interactive_control_command">		
		<MODE Description="MODE" Multiple="1" Function="" DataBegin="32" DataLength="2" HasSign="false" ColumnName="cmmd_mode" DataType="int(11)"> </MODE>		
		<MB Description="MB" Multiple="1" Function="" DataBegin="34" DataLength="1" HasSign="false" ColumnName="cmmd_mb" DataType="int(11)"> </MB>
	</FrameStruct>
	<FrameStruct ID="1004" Description="积分控制命令帧" FrameLength="32" DataTableName="integral_control_command">		
		<MODE Description="MODE" Multiple="1" Function="" DataBegin="32" DataLength="2" HasSign="false" ColumnName="cmmd_mode" DataType="int(11)"> </MODE>		
		<IFOLD Description="IFOLD" Multiple="1" Function="" DataBegin="34" DataLength="2" HasSign="false" ColumnName="cmmd_ifOld" DataType="int(11)"> </IFOLD>		
		<PN Description="方案号" Multiple="1" Function="" DataBegin="36" DataLength="2" HasSign="false" ColumnName="cmmd_pn" DataType="int(11)"> </PN>		
		<TrajDate Description="弹道日期" Multiple="1" Function="" DataBegin="38" DataLength="2" HasSign="false" ColumnName="cmmd_date" DataType="int(11)"> </TrajDate>
		<TrajTime Description="弹道时标" Multiple="1" Function="" DataBegin="40" DataLength="4" HasSign="false" ColumnName="cmmd_t" DataType="int(11)"> </TrajTime>		
		<LaunchX Description="发射系弹道X" Multiple="0.1" Function="" DataBegin="44" DataLength="4" HasSign="true" ColumnName="x" DataType="double(255, 1)"> </LaunchX>	
		<LaunchY Description="发射系弹道Y" Multiple="0.1" Function="" DataBegin="48" DataLength="4" HasSign="true" ColumnName="y" DataType="double(255, 1)"> </LaunchY>			
		<LaunchZ Description="发射系弹道Z" Multiple="0.1" Function="" DataBegin="52" DataLength="4" HasSign="true" ColumnName="z" DataType="double(255, 1)"> </LaunchZ>	
		<LaunchVX Description="发射系弹道VX" Multiple="0.01" Function="" DataBegin="56" DataLength="4" HasSign="true" ColumnName="vx" DataType="double(255, 2)"> </LaunchVX>	
		<LaunchVY Description="发射系弹道VY" Multiple="0.01" Function="" DataBegin="60" DataLength="4" HasSign="true" ColumnName="vy" DataType="double(255, 2)"> </LaunchVY>	
		<LaunchVZ Description="发射系弹道VZ" Multiple="0.01" Function="" DataBegin="64" DataLength="4" HasSign="true" ColumnName="vz" DataType="double(255, 2)"> </LaunchVZ>	
	</FrameStruct>
	<FrameStruct ID="1005" Description="融合控制命令帧" FrameLength="32" DataTableName="fuse_control_command">		
		<MODE Description="MODE" Multiple="1" Function="" DataBegin="32" DataLength="2" HasSign="false" ColumnName="cmmd_mode" DataType="int(11)"> </MODE>		
		<IFOLD Description="IFOLD" Multiple="1" Function="" DataBegin="34" DataLength="2" HasSign="false" ColumnName="cmmd_ifOld" DataType="int(11)"> </IFOLD>		
		<RESERVED Description="RESERVED" Multiple="1" Function="" DataBegin="36" DataLength="2" HasSign="false" ColumnName="cmmd_reserved" DataType="int(11)"> </RESERVED>		
		<EPN Description="EPN" Multiple="1" Function="" DataBegin="38" DataLength="2" HasSign="false" ColumnName="cmmd_epn" DataType="int(11)"> </EPN>
		<N Description="N" Multiple="1" Function="" DataBegin="40" DataLength="2" HasSign="false" ColumnName="cmmd_n" DataType="int(11)"> </N>	
	</FrameStruct>
	<FrameStruct ID="1006" Description="优先顺序控制命令帧" FrameLength="32" DataTableName="traj_priority_control_command">		
		<IFOLD Description="IFOLD" Multiple="1" Function="" DataBegin="32" DataLength="2" HasSign="false" ColumnName="cmmd_ifOld" DataType="int(11)"> </IFOLD>		
		<TrajTime Description="弹道时标" Multiple="1" Function="" DataBegin="34" DataLength="4" HasSign="false" ColumnName="cmmd_t" DataType="int(11)"> </TrajTime>						
		<RESERVED Description="RESERVED" Multiple="1" Function="" DataBegin="38" DataLength="2" HasSign="false" ColumnName="cmmd_reserved" DataType="int(11)"> </RESERVED>		
		<N Description="N" Multiple="1" Function="" DataBegin="40" DataLength="2" HasSign="false" ColumnName="cmmd_n" DataType="int(11)"> </N>	
	</FrameStruct>
	<FrameStruct ID="1007" Description="落点控制命令帧" FrameLength="32" DataTableName="integral_control_command">		
		<IFOLD Description="IFOLD" Multiple="1" Function="" DataBegin="32" DataLength="2" HasSign="false" ColumnName="cmmd_ifOld" DataType="int(11)"> </IFOLD>		
		<TrajDate Description="弹道日期" Multiple="1" Function="" DataBegin="34" DataLength="2" HasSign="false" ColumnName="cmmd_date" DataType="int(11)"> </TrajDate>
		<TrajTime Description="弹道时标" Multiple="1" Function="" DataBegin="36" DataLength="4" HasSign="false" ColumnName="cmmd_t" DataType="int(11)"> </TrajTime>				
		<PN Description="方案号" Multiple="1" Function="" DataBegin="40" DataLength="2" HasSign="false" ColumnName="cmmd_pn" DataType="int(11)"> </PN>		
		<LR Description="RESERVED" Multiple="1" Function="" DataBegin="42" DataLength="2" HasSign="false" ColumnName="cmmd_reserved" DataType="int(11)"> </LR>		
		<LX Description="LX" Multiple="0.1" Function="" DataBegin="44" DataLength="4" HasSign="true" ColumnName="lr" DataType="double(255, 1)"> </LX>	
		<LY Description="LY" Multiple="0.1" Function="" DataBegin="48" DataLength="4" HasSign="true" ColumnName="lx" DataType="double(255, 1)"> </LY>			
		<LZ Description="LZ" Multiple="0.1" Function="" DataBegin="52" DataLength="4" HasSign="true" ColumnName="lz" DataType="double(255, 1)"> </LZ>	
		<DLX Description="DLX" Multiple="0.1" Function="" DataBegin="56" DataLength="4" HasSign="true" ColumnName="dlx" DataType="double(255, 2)"> </DLX>	
		<DLZ Description="DLZ" Multiple="0.1" Function="" DataBegin="60" DataLength="4" HasSign="true" ColumnName="dlz" DataType="double(255, 2)"> </DLZ>	
		<L Description="L" Multiple="0.0000002384185791015625" Function="" DataBegin="64" DataLength="4" HasSign="true" ColumnName="l" DataType="double(255, 11)"> </L>			
		<B Description="B" Multiple="0.0000002384185791015625" Function="" DataBegin="68" DataLength="4" HasSign="true" ColumnName="b" DataType="double(255, 11)"> </B>	
	</FrameStruct>
	<FrameStruct ID="1008" Description="导调控制命令帧" FrameLength="32" DataTableName="direct_adjust_control_command">		
		<MODE Description="MODE" Multiple="1" Function="" DataBegin="32" DataLength="2" HasSign="false" ColumnName="cmmd_mode" DataType="int(11)"> </MODE>		
		<RESERVED Description="RESERVED" Multiple="1" Function="" DataBegin="34" DataLength="4" HasSign="false" ColumnName="cmmd_reserved" DataType="int(11)"> </RESERVED>		
		<N Description="N" Multiple="1" Function="" DataBegin="38" DataLength="4" HasSign="false" ColumnName="cmmd_n" DataType="int(11)"> </N>	
	</FrameStruct>
	<FrameStruct ID="1009" Description="窗口T0" FrameLength="32" DataTableName="preset_t0_frame">		
		<MODE Description="MODE" Multiple="1" Function="" DataBegin="32" DataLength="2" HasSign="false" ColumnName="cmmd_mode" DataType="int(11)"> </MODE>		
		<CmdDate Description="CmdDate" Multiple="1" Function="" DataBegin="34" DataLength="2" HasSign="false" ColumnName="cmmd_date" DataType="int(11)"> </CmdDate>
		<T0 Description="T0" Multiple="1" Function="" DataBegin="36" DataLength="4" HasSign="false" ColumnName="cmmd_t0" DataType="int(11)"> </T0>
	</FrameStruct>
	<FrameStruct ID="1010" Description="发送数据" FrameLength="32" DataTableName="ds_default_data">		
		<MODE Description="MODE" Multiple="1" Function="" DataBegin="32" DataLength="2" HasSign="false" ColumnName="mode" DataType="int(11)"> </MODE>		
	</FrameStruct>
	<FrameStruct ID="1121" Description="软件运行状态" FrameLength="32" DataTableName="process_status_frame">		
		<IP Description="IP" Multiple="1" Function="" DataBegin="32" DataLength="1" HasSign="false" ColumnName="ip" DataType="int(11)"> </IP>	
		<PID Description="PID" Multiple="1" Function="" DataBegin="36" DataLength="4" HasSign="false" ColumnName="pid" DataType="int(11)"> </PID>		
		<MB Description="MB" Multiple="1" Function="" DataBegin="40" DataLength="1" HasSign="false" ColumnName="mb" DataType="int(11)"> </MB>		
		<Version Description="Version" Multiple="1" Function="" DataBegin="41" DataLength="2" HasSign="false" ColumnName="version" DataType="int(11)"> </Version>		
		<RunMode Description="RunMode" Multiple="1" Function="" DataBegin="43" DataLength="2" HasSign="false" ColumnName="runmode" DataType="int(11)"> </RunMode>		
		<StatusDate Description="StatusDate" Multiple="1" Function="" DataBegin="45" DataLength="2" HasSign="false" ColumnName="status_date" DataType="int(11)"> </StatusDate>		
		<StatusT Description="StatusT" Multiple="1" Function="" DataBegin="47" DataLength="4" HasSign="false" ColumnName="status_t" DataType="int(11)"> </StatusT>		
	</FrameStruct>
	<FrameStruct ID="1122" Description="软件监视状态" FrameLength="32" DataTableName="process_monitor_frame">		
		<ZtNum Description="ZtNum" Multiple="1" Function="" DataBegin="32" DataLength="4" HasSign="false" ColumnName="zt_num" DataType="int(11)"> </ZtNum>		
	</FrameStruct>
</FrameStructs>
