# Aquila PDXP Backend

[![License](https://img.shields.io/badge/License-Apache%202.0-blue.svg)](https://opensource.org/licenses/Apache-2.0)
[![Go Version](https://img.shields.io/badge/Go-%3E%3D1.25.0-00ADD8.svg)](https://golang.org/)
[![Redis](https://img.shields.io/badge/Redis-%3E%3D8.0-DC382D.svg)](https://redis.io/)

## 项目概述

Aquila PDXP Backend 是一个高性能的 UDP 组播监听与 PDXP 协议解析服务，专为企业内部基于 UDP 组播的 PDXP 协议数据交换而设计。该服务能够实时监听指定的 UDP 组播地址和端口，使用线程池并发解析 PDXP 数据，维护程序状态，并通过 HTTP SSE 推送状态更新和 REST API 提供历史数据查询功能。所有数据使用 Redis Timeseries 进行高效缓存。

### 核心特性

- 🚀 **高性能并发处理**：支持多 goroutine 并发监听多个 UDP 组播地址
- ⚡ **线程池并发解析**：使用线程池并发乱序处理 PDXP 协议，存储前进行时间戳排序
- 📊 **状态管理**：维护程序状态并根据解析数据实时更新
- 📡 **SSE 状态推送**：通过 HTTP SSE 向前端推送状态更新，支持可配置推送频率和状态过滤
- 🔧 **配置驱动解析**：基于 YAML 配置的动态 PDXP 协议解析引擎
- 💾 **Redis Timeseries 缓存**：专用 Redis Timeseries 进行高效的时间序列数据缓存
- 📊 **监控与指标**：内置 Prometheus 指标收集和健康检查
- 🔒 **容错机制**：支持自动重连、背压控制和错误恢复

## 技术架构

### 技术栈

| 技术类别 | 技术选型 | 版本要求 | 说明 |
|---------|---------|---------|------|
| 编程语言 | Go | >= 1.25.0 | 高并发网络编程 |
| HTTP框架 | Echo | >= 4.x | 轻量级Web框架 |
| 数据库 | Redis | >= 8.x | 时间序列数据缓存 |
| 并发控制 | sync.Pool + Worker Pool | latest | 线程池并发处理 |
| 配置解析 | gopkg.in/yaml.v3 | latest | YAML配置文件解析 |
| 日志框架 | slog | latest | 结构化日志 |
| 监控指标 | Prometheus | latest | 系统监控 |

### 系统架构

```plaintext
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│     前端服务      │    │   Aquila后端    │    │ Redis Timeseries│
│                 │    │                 │    │                 │
│  ┌───────────┐  │    │  ┌───────────┐  │    │  ┌───────────┐  │
│  │ SSE Client│◄─┼────┼─►│ SSE Server│◄─┼────┼─►│时间序列缓存 │  │
│  └───────────┘  │    │  └─────┬─────┘  │    │  └───────────┘  │
│  ┌───────────┐  │    │  ┌─────▼─────┐  │    │                 │
│  │REST Client│◄─┼────┼─►│State Mgmt │◄─┼────┼─►               │
│  └───────────┘  │    │  └─────┬─────┘  │    │                 │
└─────────────────┘    │  ┌─────▼─────┐  │    └─────────────────┘
                       │  │REST Server│  │
┌─────────────────┐    │  └───────────┘  │
│    UDP组播源     │    │  ┌───────────┐  │
│                 │    │  │Thread Pool│  │
│  **********     │◄───┼──┤  Parser   │  │
│  :2511          │    │  └─────┬─────┘  │
│                 │    │  ┌─────▼─────┐  │
└─────────────────┘    │  │UDP Listener│ │
                       │  │ Goroutines│  │
                       │  └───────────┘  │
                       └─────────────────┘
```

## 功能模块

### 1. UDP 数据监听

- 读取并解析 `./config/config.yaml` 中的 UDP 组播配置
- 并发监听配置中的所有 UDP 组播地址
- 实现数据包完整性校验和 PDXP 载荷提取
- 支持健康检查、自动重连等容错机制

### 2. 数据存储与状态管理

- 使用 Redis Timeseries 进行高效的时间序列数据缓存
- 维护程序内部状态，根据解析的结构化数据实时更新
- 支持状态查询和历史数据检索
- 批量存储频率可配置（建议100-1000ms间隔）
- 集成背压控制机制防止内存溢出

### 3. 协议解析线程池

- 基于配置驱动的动态解析引擎
- 使用线程池并发乱序处理 PDXP 协议解析
- 支持十六进制 BID 匹配和多种数据类型
- 存储前按时间戳对所有序列进行排序
- 解析完成后触发状态更新和 SSE 推送

### 4. HTTP SSE 状态推送

- 支持跨域的 HTTP SSE 连接
- 多客户端并发连接管理（≥100 连接）
- 可配置的推送频率（1-10Hz）
- 支持状态组过滤和订阅
- 不同 SSE 连接可订阅重叠或不同的状态集合
- 每个 SSE 连接可独立配置推送频率和状态过滤条件

### 5. REST API 服务

- `GET /data` - 历史数据查询，支持分页和时间范围过滤
- `GET /state` - 程序状态查询，支持状态组过滤
- `DELETE /data` - 批量删除 Redis 缓存数据
- `GET /metrics` - Prometheus 格式的服务运行指标
- `GET /health` - 服务健康状态检查

### 6. 服务监控

- 集成 Prometheus 指标收集
- 结构化日志输出（基于 slog）
- 监控连接数、解析速度、错误率、状态更新频率等关键指标

## 性能指标

### 吞吐量

- UDP 数据接收：>= 10,000 packets/sec per address
- 同时监听：>= 10 个组播地址
- 线程池协议解析：>= 80,000 frames/sec
- SSE 并发连接：>= 100 connections
- SSE 状态推送频率：1-10Hz（可配置）
- Redis 批量存储频率：100-1000ms间隔（可配置）
- 状态管理更新：>= 10,000 updates/sec
- REST API 查询：>= 100 req/sec

### 延迟

- 端到端延迟：< 200ms (P99)
- 线程池解析延迟：< 0.5ms per frame
- 时间戳排序延迟：< 5ms per batch
- SSE 推送延迟：< 100ms
- Redis 批量存储延迟：< 50ms per batch
- REST API 响应：< 100ms (P95)
- 状态查询响应：< 50ms (P95)

## 快速开始

### 环境要求

- Go >= 1.25.0
- Redis >= 8.0
- 支持 UDP 组播的网络环境

### 安装

1. 克隆项目

```bash
git clone <repository-url>
cd aquila
```

2. 安装依赖

```bash
go mod download
```

3. 配置文件
编辑 `config/config.yaml` 配置 UDP 组播地址和 PDXP 子结构

4. 启动 Redis

```bash
redis-server
```

5. 运行服务

```bash
go run cmd/aquila/main.go
```

### 配置说明

#### 主配置文件 (`config/config.yaml`)

```yaml
# UDP 组播配置
multicast:
  - address: "**********:2511"
    description: "PDXP 数据源"
    enabled: true

# PDXP 子结构配置
pdxp:
  version: "3.0"
  structures:
    - name: "GX"
      source: "**********:2511"
      enabled: true
      bids: ["0x01", "0x02"]
```

#### PDXP 协议定义 (`config/pdxp/PDXP.yaml`)

```yaml
# PDXP 协议头结构定义
header:
  - name: "sync"
    type: "uint32"
  - name: "length"
    type: "uint16"
  - name: "bid"
    type: "uint8"
```

### API 使用示例

#### SSE 状态推送连接

```javascript
// 订阅特定状态组的更新
const eventSource = new EventSource('/sse?states=group1,group2&frequency=5');
eventSource.onmessage = function(event) {
    const stateData = JSON.parse(event.data);
    console.log('状态更新:', stateData);
};
```

#### 历史数据查询

```bash
curl "http://localhost:8080/data?start=2024-01-01T00:00:00Z&end=2024-01-02T00:00:00Z&limit=100"
```

#### 状态查询

```bash
# 查询所有状态
curl "http://localhost:8080/state"

# 查询特定状态组
curl "http://localhost:8080/state?groups=group1,group2"
```

#### 健康检查

```bash
curl http://localhost:8080/health
```

## 开发指南

### 项目结构

```plaintext
aquila/
├── cmd/                   # 主程序入口
│   └── aquila/
│       └── main.go
├── internal/              # 内部包，不对外暴露
├── pkg/                   # 可复用的公共包
├── api/                   # API 文档和定义
├── config/                # 配置文件
├── docs/                  # 项目文档
├── scripts/               # 构建和部署脚本
└── tests/                 # 测试文件
```

### 开发规范

- 严格遵循 Go 官方代码规范
- 使用有意义的变量和函数命名
- 每个包必须有 package 注释
- 所有函数和结构体必须有详细注释
- 单元测试覆盖率 >= 80%

### 构建和测试

```bash
# 运行测试
go test ./...

# 构建
go build -o bin/aquila cmd/aquila/main.go

# 代码格式化
go fmt ./...

# 代码检查
go vet ./...
```

## 监控和运维

### Prometheus 指标

访问 `http://localhost:8080/metrics` 获取以下指标：

- `aquila_udp_packets_received_total` - UDP 数据包接收总数
- `aquila_pdxp_frames_parsed_total` - PDXP 帧解析总数
- `aquila_thread_pool_workers_active` - 活跃线程池工作者数量
- `aquila_thread_pool_queue_size` - 线程池任务队列大小
- `aquila_state_updates_total` - 状态更新总数
- `aquila_sse_connections_active` - 活跃 SSE 连接数
- `aquila_sse_messages_sent_total` - SSE 消息发送总数
- `aquila_redis_timeseries_operations_total` - Redis Timeseries 操作总数
- `aquila_processing_duration_seconds` - 处理延迟分布
- `aquila_sorting_duration_seconds` - 时间戳排序延迟分布

### 日志

服务使用结构化日志，支持多级别输出：

```bash
# 查看实时日志
tail -f logs/aquila.log

# 过滤错误日志
grep "ERROR" logs/aquila.log
```

## 许可证

本项目采用 [Apache License 2.0](LICENSE) 许可证。

## 贡献

欢迎提交 Issue 和 Pull Request 来改进项目。请确保：

1. 遵循项目的代码规范
2. 添加适当的测试用例
3. 更新相关文档
4. 提交前运行所有测试

## 支持

如有问题或建议，请通过以下方式联系：

- 提交 [GitHub Issue](../../issues)
- 查看 [项目文档](docs/)
- 参考 [API 文档](api/aquila.yaml)
