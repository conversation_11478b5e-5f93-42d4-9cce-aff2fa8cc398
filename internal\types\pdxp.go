// Package types 定义 PDXP 协议相关的核心数据类型
package types

import (
	"fmt"
	"strconv"
	"time"
)

// PDXPFrameType PDXP 帧类型配置
// 对应 config.yaml 中的 pdxp 配置项
type PDXPFrameType struct {
	Name   string   `yaml:"name" json:"name"`     // 帧类型的唯一名称
	Key    string   `yaml:"key" json:"key"`       // 帧结构定义的唯一名称
	Desc   string   `yaml:"desc" json:"desc"`     // 描述信息
	Src    string   `yaml:"src" json:"src"`       // 数据源名称，对应UDP端点名称
	Enable bool     `yaml:"enable" json:"enable"` // 是否启用
	BIDs   []uint32 `yaml:"bids" json:"bids"`     // 对应的BID列表
}

// PDXPConfig PDXP 配置结构
type PDXPConfig struct {
	PDXP []PDXPFrameType `yaml:"pdxp" json:"pdxp"` // PDXP 帧类型列表
}

// Validate 验证PDXP配置
func (c *PDXPConfig) Validate() error {
	if len(c.PDXP) == 0 {
		return fmt.Errorf("PDXP配置不能为空")
	}

	names := make(map[string]bool)
	bids := make(map[uint32]*PDXPFrameType)

	for i, frameType := range c.PDXP {
		// 验证必填字段
		if frameType.Name == "" {
			return fmt.Errorf("第%d个PDXP帧类型的名称不能为空", i+1)
		}
		if frameType.Key == "" {
			return fmt.Errorf("PDXP帧类型'%s'的key不能为空", frameType.Name)
		}
		if frameType.Src == "" {
			return fmt.Errorf("PDXP帧类型'%s'的数据源不能为空", frameType.Name)
		}
		if len(frameType.BIDs) == 0 {
			return fmt.Errorf("PDXP帧类型'%s'的BID列表不能为空", frameType.Name)
		}

		// 验证名称唯一性
		if names[frameType.Name] {
			return fmt.Errorf("PDXP帧类型名称'%s'重复", frameType.Name)
		}
		names[frameType.Name] = true

		// 验证BID唯一性
		for _, bid := range frameType.BIDs {
			if existingFrameType, exists := bids[bid]; exists {
				return fmt.Errorf("BID 0x%08X 同时被帧类型'%s'和'%s'使用",
					bid, existingFrameType.Name, frameType.Name)
			}
			bids[bid] = &frameType
		}
	}

	return nil
}

// GetFrameTypeByBID 根据BID查找帧类型
func (c *PDXPConfig) GetFrameTypeByBID(bid uint32) *PDXPFrameType {
	for _, frameType := range c.PDXP {
		if !frameType.Enable {
			continue
		}
		for _, frameBid := range frameType.BIDs {
			if frameBid == bid {
				return &frameType
			}
		}
	}
	return nil
}

// GetFrameTypesBySource 根据数据源获取帧类型列表
func (c *PDXPConfig) GetFrameTypesBySource(src string) []PDXPFrameType {
	var types []PDXPFrameType
	for _, frameType := range c.PDXP {
		if frameType.Enable && frameType.Src == src {
			types = append(types, frameType)
		}
	}
	return types
}

// GetEnabledFrameTypes 获取所有启用的帧类型
func (c *PDXPConfig) GetEnabledFrameTypes() []PDXPFrameType {
	var enabled []PDXPFrameType
	for _, frameType := range c.PDXP {
		if frameType.Enable {
			enabled = append(enabled, frameType)
		}
	}
	return enabled
}

// PDXPSchema PDXP 协议模式定义
// 对应 config/pdxp/*.yaml 文件中的结构定义
type PDXPSchema struct {
	Version string                   `yaml:"version" json:"version"`                   // 协议版本
	Name    string                   `yaml:"name,omitempty" json:"name,omitempty"`     // 协议名称
	Header  *PDXPBlockDef            `yaml:"header,omitempty" json:"header,omitempty"` // 协议头定义
	Blocks  map[string]*PDXPBlockDef `yaml:",inline" json:"blocks"`                    // 数据块定义
}

// PDXPBlockDef 数据块定义
type PDXPBlockDef struct {
	Desc    string            `yaml:"desc" json:"desc"`                           // 描述
	Class   string            `yaml:"class" json:"class"`                         // 类别: block, field, key, word
	Type    string            `yaml:"type" json:"type"`                           // 类型: static, dynamic, repeated 或基础数据类型
	Len     *int              `yaml:"len,omitempty" json:"len,omitempty"`         // 静态块长度
	Count   string            `yaml:"count,omitempty" json:"count,omitempty"`     // 循环次数或字段引用
	Func    string            `yaml:"func,omitempty" json:"func,omitempty"`       // 转换函数
	Members []*PDXPBlockDef   `yaml:"members,omitempty" json:"members,omitempty"` // 成员定义
	Values  map[string]string `yaml:"values,omitempty" json:"values,omitempty"`   // 枚举值定义
	Name    string            `json:"name,omitempty"`                             // 字段名称，在解析时填充
}

// PDXPField 解析后的字段数据
type PDXPField struct {
	Name     string                `json:"name"`               // 字段名称
	Desc     string                `json:"desc"`               // 字段描述
	Class    string                `json:"class"`              // 字段类别
	Type     string                `json:"type"`               // 字段类型
	RawValue any                   `json:"raw_value"`          // 原始值
	Value    any                   `json:"value"`              // 转换后的值
	Children map[string]*PDXPField `json:"children,omitempty"` // 子字段
	Array    []*PDXPField          `json:"array,omitempty"`    // 数组字段
}

// PDXPPacket 完整的PDXP数据包
type PDXPPacket struct {
	Header    map[string]*PDXPField `json:"header"`     // 包头（动态解析）
	Data      map[string]*PDXPField `json:"data"`       // 解析后的数据域
	FrameType *PDXPFrameType        `json:"frame_type"` // 帧类型信息
	Timestamp time.Time             `json:"timestamp"`  // 解析时间戳
	Source    string                `json:"source"`     // 数据源
	RawData   []byte                `json:"-"`          // 原始数据（不序列化）
	Size      int                   `json:"size"`       // 数据包大小
}

// GetBID 获取Header中的BID字段值
func (p *PDXPPacket) GetBID() uint32 {
	if bidField, exists := p.Header["Bid"]; exists && bidField.Value != nil {
		if bid, ok := bidField.Value.(uint32); ok {
			return bid
		}
	}
	return 0
}

// GetTimestamp 从Header字段计算时间戳
func (p *PDXPPacket) GetTimestamp() time.Time {
	var date, timeVal uint32

	// 获取Date字段
	if dateField, exists := p.Header["Date"]; exists && dateField.Value != nil {
		if d, ok := dateField.Value.(uint16); ok {
			date = uint32(d)
		}
	}

	// 获取Time字段
	if timeField, exists := p.Header["Time"]; exists && timeField.Value != nil {
		if t, ok := timeField.Value.(uint32); ok {
			timeVal = t
		}
	}

	// PDXP时间格式转换: Date为距离2000年1月1日的天数，Time为当天的秒数*10
	baseTime := time.Date(2000, 1, 1, 0, 0, 0, 0, time.UTC)
	days := time.Duration(date) * 24 * time.Hour
	seconds := time.Duration(timeVal/10) * time.Second
	return baseTime.Add(days).Add(seconds)
}

// GetRedisKey 生成Redis时序数据的键名
func (p *PDXPPacket) GetRedisKey(fieldPath string) string {
	return fmt.Sprintf("pdxp:%s:%s:%08X:%s",
		p.Source, p.FrameType.Name, p.GetBID(), fieldPath)
}

// GetFieldValue 递归获取指定路径的字段值
// 支持的路径格式：
// - "field" - 直接字段访问
// - "field.subfield" - 嵌套字段访问
// - "field[0]" - 数组元素访问
// - "field.subfield[1].value" - 复合路径访问
// - "header.Bid" - Header字段访问
func (p *PDXPPacket) GetFieldValue(path string) any {
	if field := p.getFieldByPath(path); field != nil {
		return field.Value
	}
	return nil
}

// getFieldByPath 根据路径获取字段
func (p *PDXPPacket) getFieldByPath(path string) *PDXPField {
	if path == "" {
		return nil
	}

	// 解析路径组件
	components := parseFieldPath(path)
	if len(components) == 0 {
		return nil
	}

	// 确定起始字段集合
	var currentFields map[string]*PDXPField
	firstComponent := components[0]

	// 检查是否访问Header字段
	if firstComponent.Name == "header" {
		currentFields = p.Header
		components = components[1:] // 跳过"header"前缀
	} else {
		currentFields = p.Data
	}

	// 如果只有一个组件且是header访问，直接返回
	if len(components) == 0 {
		return nil // "header"本身不是字段
	}

	// 递归解析路径
	return resolveFieldPath(currentFields, components)
}

// FieldPathComponent 字段路径组件
type FieldPathComponent struct {
	Name  string // 字段名称
	Index int    // 数组索引，-1表示不是数组访问
}

// parseFieldPath 解析字段路径为组件列表
// 支持格式：field, field.subfield, field[0], field.subfield[1].value
func parseFieldPath(path string) []FieldPathComponent {
	if path == "" {
		return nil
	}

	var components []FieldPathComponent
	var current string
	var inBracket bool
	var bracketContent string

	for _, char := range path {
		switch char {
		case '.':
			if inBracket {
				bracketContent += string(char)
			} else {
				// 处理当前组件
				if current != "" {
					components = append(components, FieldPathComponent{
						Name:  current,
						Index: -1,
					})
					current = ""
				}
			}
		case '[':
			if inBracket {
				// 嵌套括号，添加到内容中
				bracketContent += string(char)
			} else {
				inBracket = true
				bracketContent = ""
			}
		case ']':
			if inBracket {
				// 解析数组索引
				index := -1
				if bracketContent != "" {
					if idx, err := strconv.Atoi(bracketContent); err == nil {
						index = idx
					}
				}
				components = append(components, FieldPathComponent{
					Name:  current,
					Index: index,
				})
				current = ""
				inBracket = false
				bracketContent = ""
			}
		default:
			if inBracket {
				bracketContent += string(char)
			} else {
				current += string(char)
			}
		}
	}

	// 处理最后一个组件
	if current != "" {
		components = append(components, FieldPathComponent{
			Name:  current,
			Index: -1,
		})
	}

	return components
}

// resolveFieldPath 递归解析字段路径
func resolveFieldPath(fields map[string]*PDXPField, components []FieldPathComponent) *PDXPField {
	if len(components) == 0 || fields == nil {
		return nil
	}

	component := components[0]
	field, exists := fields[component.Name]
	if !exists {
		return nil
	}

	// 如果这是最后一个组件
	if len(components) == 1 {
		// 检查是否需要数组访问
		if component.Index >= 0 {
			if field.Array != nil && component.Index < len(field.Array) {
				return field.Array[component.Index]
			}
			return nil
		}
		return field
	}

	// 还有更多组件需要解析
	remainingComponents := components[1:]

	// 如果当前组件需要数组访问
	if component.Index >= 0 {
		if field.Array != nil && component.Index < len(field.Array) {
			arrayElement := field.Array[component.Index]
			if arrayElement.Children != nil {
				return resolveFieldPath(arrayElement.Children, remainingComponents)
			}
		}
		return nil
	}

	// 继续解析嵌套字段
	if field.Children != nil {
		return resolveFieldPath(field.Children, remainingComponents)
	}

	return nil
}

// PDXPParser PDXP解析器接口
type PDXPParser interface {
	// LoadSchema 加载协议模式
	LoadSchema(schemaPath string) error

	// Parse 解析PDXP数据包
	Parse(data []byte, frameType *PDXPFrameType) (*PDXPPacket, error)

	// GetSupportedFrameTypes 获取支持的帧类型列表
	GetSupportedFrameTypes() []string

	// ValidatePacket 验证数据包完整性
	ValidatePacket(packet *PDXPPacket) error
}

// PDXPStats PDXP解析统计信息
type PDXPStats struct {
	TotalPackets   int64            `json:"total_packets"`    // 总解析包数
	SuccessPackets int64            `json:"success_packets"`  // 成功解析包数
	ErrorPackets   int64            `json:"error_packets"`    // 解析失败包数
	ParseRate      float64          `json:"parse_rate"`       // 解析速率(包/秒)
	SuccessRate    float64          `json:"success_rate"`     // 成功率
	AvgParseTime   float64          `json:"avg_parse_time"`   // 平均解析时间(毫秒)
	LastParseTime  time.Time        `json:"last_parse_time"`  // 最后解析时间
	FrameTypeStats map[string]int64 `json:"frame_type_stats"` // 按帧类型统计
}
