// Package types 定义 Aquila 系统的错误处理相关类型
package types

import (
	"fmt"
	"time"
)

// AquilaError Aquila 系统专用错误类型
// 提供详细的错误信息和中文错误消息
type AquilaError struct {
	Code      ErrorCode      `json:"code"`      // 错误代码
	Type      ErrorType      `json:"type"`      // 错误类型
	Message   string         `json:"message"`   // 错误消息
	Source    string         `json:"source"`    // 错误源
	Timestamp time.Time      `json:"timestamp"` // 发生时间
	Context   map[string]any `json:"context"`   // 错误上下文
	Cause     error          `json:"-"`         // 原始错误（不序列化）
}

// ErrorCode 错误代码枚举
type ErrorCode string

// 配置相关错误代码
const (
	ErrorCodeConfigFileNotFound     ErrorCode = "CONFIG_FILE_NOT_FOUND"    // 配置文件未找到
	ErrorCodeConfigFileReadFailed   ErrorCode = "CONFIG_FILE_READ_FAILED"  // 配置文件读取失败
	ErrorCodeConfigParseFailed      ErrorCode = "CONFIG_PARSE_FAILED"      // 配置解析失败
	ErrorCodeConfigValidationFailed ErrorCode = "CONFIG_VALIDATION_FAILED" // 配置验证失败
	ErrorCodeSchemaFileNotFound     ErrorCode = "SCHEMA_FILE_NOT_FOUND"    // 模式文件未找到
	ErrorCodeSchemaLoadFailed       ErrorCode = "SCHEMA_LOAD_FAILED"       // 模式加载失败
)

// 网络相关错误代码
const (
	ErrorCodeUDPBindFailed    ErrorCode = "UDP_BIND_FAILED"    // UDP绑定失败
	ErrorCodeUDPListenFailed  ErrorCode = "UDP_LISTEN_FAILED"  // UDP监听失败
	ErrorCodeUDPReceiveFailed ErrorCode = "UDP_RECEIVE_FAILED" // UDP接收失败
	ErrorCodeNetworkTimeout   ErrorCode = "NETWORK_TIMEOUT"    // 网络超时
	ErrorCodeConnectionLost   ErrorCode = "CONNECTION_LOST"    // 连接丢失
	ErrorCodeInvalidAddress   ErrorCode = "INVALID_ADDRESS"    // 无效地址
	ErrorCodePortInUse        ErrorCode = "PORT_IN_USE"        // 端口被占用
)

// 解析相关错误代码
const (
	ErrorCodeParseHeaderFailed     ErrorCode = "PARSE_HEADER_FAILED"     // 解析包头失败
	ErrorCodeParseDataFailed       ErrorCode = "PARSE_DATA_FAILED"       // 解析数据失败
	ErrorCodeInvalidPacketSize     ErrorCode = "INVALID_PACKET_SIZE"     // 数据包大小无效
	ErrorCodeUnsupportedFrameType  ErrorCode = "UNSUPPORTED_FRAME_TYPE"  // 不支持的帧类型
	ErrorCodeSchemaNotFound        ErrorCode = "SCHEMA_NOT_FOUND"        // 模式未找到
	ErrorCodeFieldValidationFailed ErrorCode = "FIELD_VALIDATION_FAILED" // 字段验证失败
	ErrorCodeDataCorrupted         ErrorCode = "DATA_CORRUPTED"          // 数据损坏
)

// 存储相关错误代码
const (
	ErrorCodeRedisConnectionFailed ErrorCode = "REDIS_CONNECTION_FAILED" // Redis连接失败
	ErrorCodeRedisWriteFailed      ErrorCode = "REDIS_WRITE_FAILED"      // Redis写入失败
	ErrorCodeRedisReadFailed       ErrorCode = "REDIS_READ_FAILED"       // Redis读取失败
	ErrorCodeDiskSpaceFull         ErrorCode = "DISK_SPACE_FULL"         // 磁盘空间满
	ErrorCodeFileWriteFailed       ErrorCode = "FILE_WRITE_FAILED"       // 文件写入失败
)

// 系统相关错误代码
const (
	ErrorCodeMemoryExhausted    ErrorCode = "MEMORY_EXHAUSTED"    // 内存耗尽
	ErrorCodeCPUOverloaded      ErrorCode = "CPU_OVERLOADED"      // CPU过载
	ErrorCodeServiceUnavailable ErrorCode = "SERVICE_UNAVAILABLE" // 服务不可用
	ErrorCodeInternalError      ErrorCode = "INTERNAL_ERROR"      // 内部错误
	ErrorCodePermissionDenied   ErrorCode = "PERMISSION_DENIED"   // 权限拒绝
)

// Error 实现error接口
func (e *AquilaError) Error() string {
	return e.Message
}

// WithCause 设置原始错误
func (e *AquilaError) WithCause(cause error) *AquilaError {
	e.Cause = cause
	return e
}

// WithContext 添加上下文信息
func (e *AquilaError) WithContext(key string, value any) *AquilaError {
	if e.Context == nil {
		e.Context = make(map[string]any)
	}
	e.Context[key] = value
	return e
}

// WithSource 设置错误源
func (e *AquilaError) WithSource(source string) *AquilaError {
	e.Source = source
	return e
}

// GetSeverity 根据错误代码获取严重程度
func (e *AquilaError) GetSeverity() ErrorSeverity {
	switch e.Code {
	case ErrorCodeConfigFileNotFound, ErrorCodeSchemaFileNotFound:
		return SeverityFatal
	case ErrorCodeMemoryExhausted, ErrorCodeServiceUnavailable:
		return SeverityCritical
	case ErrorCodeUDPBindFailed, ErrorCodeRedisConnectionFailed:
		return SeverityError
	case ErrorCodeNetworkTimeout, ErrorCodeConnectionLost:
		return SeverityWarning
	default:
		return SeverityError
	}
}

// NewAquilaError 创建新的Aquila错误
func NewAquilaError(code ErrorCode, errorType ErrorType, message string) *AquilaError {
	return &AquilaError{
		Code:      code,
		Type:      errorType,
		Message:   message,
		Timestamp: time.Now(),
		Context:   make(map[string]any),
	}
}

// 预定义错误工厂函数

// NewConfigError 创建配置错误
func NewConfigError(code ErrorCode, details string, args ...any) *AquilaError {
	messages := map[ErrorCode]string{
		ErrorCodeConfigFileNotFound:     "配置文件未找到: %s",
		ErrorCodeConfigFileReadFailed:   "配置文件读取失败: %s",
		ErrorCodeConfigParseFailed:      "配置文件解析失败: %s",
		ErrorCodeConfigValidationFailed: "配置验证失败: %s",
		ErrorCodeSchemaFileNotFound:     "协议模式文件未找到: %s",
		ErrorCodeSchemaLoadFailed:       "协议模式加载失败: %s",
	}

	template := messages[code]
	if template == "" {
		template = "配置错误: %s"
	}

	message := fmt.Sprintf(template, details)
	if len(args) > 0 {
		message = fmt.Sprintf(message, args...)
	}

	return NewAquilaError(code, ErrorTypeConfig, message)
}

// NewNetworkError 创建网络错误
func NewNetworkError(code ErrorCode, details string, args ...any) *AquilaError {
	messages := map[ErrorCode]string{
		ErrorCodeUDPBindFailed:    "UDP端口绑定失败: %s",
		ErrorCodeUDPListenFailed:  "UDP监听启动失败: %s",
		ErrorCodeUDPReceiveFailed: "UDP数据接收失败: %s",
		ErrorCodeNetworkTimeout:   "网络操作超时: %s",
		ErrorCodeConnectionLost:   "网络连接丢失: %s",
		ErrorCodeInvalidAddress:   "网络地址格式无效: %s",
		ErrorCodePortInUse:        "端口已被占用: %s",
	}

	template := messages[code]
	if template == "" {
		template = "网络错误: %s"
	}

	message := fmt.Sprintf(template, details)
	if len(args) > 0 {
		message = fmt.Sprintf(message, args...)
	}

	return NewAquilaError(code, ErrorTypeNetwork, message)
}

// NewParseError 创建解析错误
func NewParseError(code ErrorCode, details string, args ...any) *AquilaError {
	messages := map[ErrorCode]string{
		ErrorCodeParseHeaderFailed:     "PDXP包头解析失败: %s",
		ErrorCodeParseDataFailed:       "PDXP数据域解析失败: %s",
		ErrorCodeInvalidPacketSize:     "数据包大小无效，期望 %d 字节，实际 %d 字节: %s",
		ErrorCodeUnsupportedFrameType:  "不支持的帧类型 BID=0x%08X: %s",
		ErrorCodeSchemaNotFound:        "未找到对应的协议模式 '%s': %s",
		ErrorCodeFieldValidationFailed: "字段验证失败 '%s': %s",
		ErrorCodeDataCorrupted:         "数据包损坏或格式错误: %s",
	}

	template := messages[code]
	if template == "" {
		template = "解析错误: %s"
	}

	message := fmt.Sprintf(template, details)
	if len(args) > 0 {
		message = fmt.Sprintf(message, args...)
	}

	return NewAquilaError(code, ErrorTypeParsing, message)
}

// NewStorageError 创建存储错误
func NewStorageError(code ErrorCode, details string, args ...any) *AquilaError {
	messages := map[ErrorCode]string{
		ErrorCodeRedisConnectionFailed: "Redis连接失败: %s",
		ErrorCodeRedisWriteFailed:      "Redis写入失败: %s",
		ErrorCodeRedisReadFailed:       "Redis读取失败: %s",
		ErrorCodeDiskSpaceFull:         "磁盘空间不足: %s",
		ErrorCodeFileWriteFailed:       "文件写入失败: %s",
	}

	template := messages[code]
	if template == "" {
		template = "存储错误: %s"
	}

	message := fmt.Sprintf(template, details)
	if len(args) > 0 {
		message = fmt.Sprintf(message, args...)
	}

	return NewAquilaError(code, ErrorTypeStorage, message)
}

// NewSystemError 创建系统错误
func NewSystemError(code ErrorCode, details string, args ...any) *AquilaError {
	messages := map[ErrorCode]string{
		ErrorCodeMemoryExhausted:    "系统内存不足: %s",
		ErrorCodeCPUOverloaded:      "系统CPU过载: %s",
		ErrorCodeServiceUnavailable: "服务暂时不可用: %s",
		ErrorCodeInternalError:      "系统内部错误: %s",
		ErrorCodePermissionDenied:   "操作权限不足: %s",
	}

	template := messages[code]
	if template == "" {
		template = "系统错误: %s"
	}

	message := fmt.Sprintf(template, details)
	if len(args) > 0 {
		message = fmt.Sprintf(message, args...)
	}

	return NewAquilaError(code, ErrorTypeSystem, message)
}

// ErrorHandler 错误处理器接口
type ErrorHandler interface {
	// HandleError 处理错误
	HandleError(err *AquilaError)

	// ShouldRetry 判断错误是否应该重试
	ShouldRetry(err *AquilaError) bool

	// GetRetryDelay 获取重试延迟时间
	GetRetryDelay(err *AquilaError, retryCount int) time.Duration
}

// Logger 日志接口
type Logger interface {
	Info(msg string, args ...any)
	Warn(msg string, args ...any)
	Error(msg string, args ...any)
}

// DefaultErrorHandler 默认错误处理器
type DefaultErrorHandler struct {
	logger Logger
}

// NewDefaultErrorHandler 创建默认错误处理器
func NewDefaultErrorHandler(logger Logger) *DefaultErrorHandler {
	return &DefaultErrorHandler{
		logger: logger,
	}
}

// HandleError 处理错误
func (h *DefaultErrorHandler) HandleError(err *AquilaError) {
	severity := err.GetSeverity()

	switch severity {
	case SeverityFatal, SeverityCritical:
		h.logger.Error("严重错误", "code", string(err.Code), "message", err.Message, "source", err.Source)
	case SeverityError:
		h.logger.Error("错误", "code", string(err.Code), "message", err.Message, "source", err.Source)
	case SeverityWarning:
		h.logger.Warn("警告", "code", string(err.Code), "message", err.Message, "source", err.Source)
	case SeverityInfo:
		h.logger.Info("信息", "code", string(err.Code), "message", err.Message, "source", err.Source)
	}
}

// ShouldRetry 判断错误是否应该重试
func (h *DefaultErrorHandler) ShouldRetry(err *AquilaError) bool {
	// 可重试的错误类型
	retryableCodes := map[ErrorCode]bool{
		ErrorCodeNetworkTimeout:        true,
		ErrorCodeConnectionLost:        true,
		ErrorCodeUDPReceiveFailed:      true,
		ErrorCodeRedisConnectionFailed: true,
		ErrorCodeRedisWriteFailed:      true,
		ErrorCodeRedisReadFailed:       true,
		ErrorCodeServiceUnavailable:    true,
	}

	// 不应重试的严重错误
	if err.GetSeverity() == SeverityFatal {
		return false
	}

	return retryableCodes[err.Code]
}

// GetRetryDelay 获取重试延迟时间（指数退避算法）
func (h *DefaultErrorHandler) GetRetryDelay(err *AquilaError, retryCount int) time.Duration {
	baseDelay := time.Second

	// 根据错误类型调整基础延迟
	switch err.Code {
	case ErrorCodeNetworkTimeout, ErrorCodeConnectionLost:
		baseDelay = 2 * time.Second
	case ErrorCodeRedisConnectionFailed:
		baseDelay = 5 * time.Second
	case ErrorCodeServiceUnavailable:
		baseDelay = 10 * time.Second
	}

	// 指数退避，最大延迟60秒
	delay := min(baseDelay*time.Duration(1<<uint(retryCount)), 60*time.Second)

	return delay
}

// WrapError 包装标准错误为AquilaError
func WrapError(err error, code ErrorCode, errorType ErrorType, message string) *AquilaError {
	if err == nil {
		return nil
	}

	// 如果已经是AquilaError，直接返回
	if aquilaErr, ok := err.(*AquilaError); ok {
		return aquilaErr
	}

	return NewAquilaError(code, errorType, message).WithCause(err)
}

// IsAquilaError 检查是否为AquilaError
func IsAquilaError(err error) (*AquilaError, bool) {
	if aquilaErr, ok := err.(*AquilaError); ok {
		return aquilaErr, true
	}
	return nil, false
}

// HasErrorCode 检查错误是否包含特定错误代码
func HasErrorCode(err error, code ErrorCode) bool {
	if aquilaErr, ok := IsAquilaError(err); ok {
		return aquilaErr.Code == code
	}
	return false
}

// ErrorStats 错误统计信息
type ErrorStats struct {
	TotalErrors      int64                   `json:"total_errors"`       // 总错误数
	ErrorsByType     map[ErrorType]int64     `json:"errors_by_type"`     // 按类型统计
	ErrorsByCode     map[ErrorCode]int64     `json:"errors_by_code"`     // 按代码统计
	ErrorsBySeverity map[ErrorSeverity]int64 `json:"errors_by_severity"` // 按严重程度统计
	LastErrors       []*AquilaError          `json:"last_errors"`        // 最近的错误
	StartTime        time.Time               `json:"start_time"`         // 统计开始时间
	LastUpdate       time.Time               `json:"last_update"`        // 最后更新时间
}
