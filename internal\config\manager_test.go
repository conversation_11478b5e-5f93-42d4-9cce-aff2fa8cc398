// Package config 配置管理器单元测试
package config

import (
	"context"
	"fmt"
	"os"
	"path/filepath"
	"sync"
	"testing"
	"time"

	"aquila/internal/types"

	"gopkg.in/yaml.v3"
)

// MockLogger 模拟日志记录器
type MockLogger struct {
	logs []LogEntry
	mu   sync.Mutex
}

type LogEntry struct {
	Level string
	Msg   string
	Args  []any
}

func NewMockLogger() *MockLogger {
	return &MockLogger{
		logs: make([]LogEntry, 0),
	}
}

func (m *MockLogger) Info(msg string, args ...any) {
	m.mu.Lock()
	defer m.mu.Unlock()
	m.logs = append(m.logs, LogEntry{Level: "info", Msg: msg, Args: args})
}

func (m *MockLogger) Warn(msg string, args ...any) {
	m.mu.Lock()
	defer m.mu.Unlock()
	m.logs = append(m.logs, LogEntry{Level: "warn", Msg: msg, Args: args})
}

func (m *MockLogger) Error(msg string, args ...any) {
	m.mu.Lock()
	defer m.mu.Unlock()
	m.logs = append(m.logs, LogEntry{Level: "error", Msg: msg, Args: args})
}

func (m *MockLogger) GetLogs() []LogEntry {
	m.mu.Lock()
	defer m.mu.Unlock()
	logs := make([]LogEntry, len(m.logs))
	copy(logs, m.logs)
	return logs
}

func (m *MockLogger) Clear() {
	m.mu.Lock()
	defer m.mu.Unlock()
	m.logs = m.logs[:0]
}

// MockConfigWatcher 模拟配置监听器
type MockConfigWatcher struct {
	configs []*Config // 使用指针避免复制锁
	errors  []error
	mu      sync.Mutex
}

func NewMockConfigWatcher() *MockConfigWatcher {
	return &MockConfigWatcher{
		configs: make([]*Config, 0),
		errors:  make([]error, 0),
	}
}

func (m *MockConfigWatcher) OnConfigChanged(config *Config) error {
	m.mu.Lock()
	defer m.mu.Unlock()
	if config != nil {
		// 创建配置的副本，避免复制锁
		configCopy := &Config{
			AppConfig:  config.AppConfig,
			ConfigPath: config.ConfigPath,
			LoadTime:   config.LoadTime,
			Version:    config.Version,
		}
		m.configs = append(m.configs, configCopy)
	}
	return nil
}

func (m *MockConfigWatcher) GetConfigs() []*Config {
	m.mu.Lock()
	defer m.mu.Unlock()
	configs := make([]*Config, len(m.configs))
	copy(configs, m.configs)
	return configs
}

// createTestConfig 创建测试配置文件
func createTestConfig(t *testing.T) string {
	t.Helper()

	// 创建临时目录
	tempDir := t.TempDir()
	configPath := filepath.Join(tempDir, "test_config.yaml")

	config := map[string]any{
		"udp": []map[string]any{
			{
				"name":   "test_tracking",
				"addr":   "**********",
				"port":   2511,
				"desc":   "测试追踪数据",
				"enable": true,
			},
			{
				"name":   "test_telemetry",
				"addr":   "230.25.1.2",
				"port":   2512,
				"desc":   "测试遥测数据",
				"enable": false,
			},
		},
		"pdxp": []map[string]any{
			{
				"name":   "test_gx",
				"key":    "GX",
				"desc":   "测试光学设备测量帧",
				"src":    "test_tracking",
				"enable": true,
				"bids":   []uint32{0x00100101, 0x00100102},
			},
			{
				"name":   "test_ml",
				"key":    "ML",
				"desc":   "测试激光雷达测量帧",
				"src":    "test_tracking",
				"enable": true,
				"bids":   []uint32{0x00100404},
			},
		},
	}

	data, err := yaml.Marshal(config)
	if err != nil {
		t.Fatalf("创建测试配置失败: %v", err)
	}

	if err := os.WriteFile(configPath, data, 0644); err != nil {
		t.Fatalf("写入测试配置文件失败: %v", err)
	}

	return configPath
}

// createTestSchemaDir 创建测试模式目录
func createTestSchemaDir(t *testing.T) string {
	t.Helper()

	tempDir := t.TempDir()
	schemaDir := filepath.Join(tempDir, "pdxp")
	if err := os.MkdirAll(schemaDir, 0755); err != nil {
		t.Fatalf("创建模式目录失败: %v", err)
	}

	// 创建GX模式文件
	gxSchema := map[string]any{
		"version": "3.0",
		"GX": map[string]any{
			"desc":  "光电设备测量帧",
			"class": "block",
			"type":  "dynamic",
			"members": []map[string]any{
				{
					"name":  "T",
					"desc":  "当前时刻",
					"class": "field",
					"type":  "uint32",
					"func":  "time",
				},
				{
					"name":  "Status",
					"desc":  "设备状态",
					"class": "field",
					"type":  "uint8",
					"func":  "hex",
				},
			},
		},
	}

	gxData, err := yaml.Marshal(gxSchema)
	if err != nil {
		t.Fatalf("创建GX模式失败: %v", err)
	}

	gxPath := filepath.Join(schemaDir, "GX.yaml")
	if err := os.WriteFile(gxPath, gxData, 0644); err != nil {
		t.Fatalf("写入GX模式文件失败: %v", err)
	}

	// 创建ML模式文件
	mlSchema := map[string]any{
		"version": "3.0",
		"ML": map[string]any{
			"desc":  "脉冲激光雷达测量帧",
			"class": "block",
			"type":  "dynamic",
			"members": []map[string]any{
				{
					"name":  "T",
					"desc":  "当前时刻",
					"class": "field",
					"type":  "uint32",
					"func":  "time",
				},
			},
		},
	}

	mlData, err := yaml.Marshal(mlSchema)
	if err != nil {
		t.Fatalf("创建ML模式失败: %v", err)
	}

	mlPath := filepath.Join(schemaDir, "ML.yaml")
	if err := os.WriteFile(mlPath, mlData, 0644); err != nil {
		t.Fatalf("写入ML模式文件失败: %v", err)
	}

	return schemaDir
}

// createInvalidConfig 创建无效的配置文件
func createInvalidConfig(t *testing.T) string {
	t.Helper()

	tempDir := t.TempDir()
	configPath := filepath.Join(tempDir, "invalid_config.yaml")

	invalidYaml := `
udp:
- name: test_tracking
  addr: invalid_ip
  port: -1
  desc: "无效配置"
  enable: true
pdxp: [
`

	if err := os.WriteFile(configPath, []byte(invalidYaml), 0644); err != nil {
		t.Fatalf("写入无效配置文件失败: %v", err)
	}

	return configPath
}

// TestNewManager 测试配置管理器创建
func TestNewManager(t *testing.T) {
	tests := []struct {
		name       string
		configPath string
		schemaDir  string
		logger     types.Logger
	}{
		{
			name:       "正常创建配置管理器",
			configPath: "/test/config.yaml",
			schemaDir:  "/test/pdxp",
			logger:     NewMockLogger(),
		},
		{
			name:       "空路径创建配置管理器",
			configPath: "",
			schemaDir:  "",
			logger:     NewMockLogger(),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			manager := NewManager(tt.configPath, tt.schemaDir, tt.logger)

			if manager == nil {
				t.Fatal("配置管理器创建失败，返回nil")
			}
			if manager.configPath != tt.configPath {
				t.Errorf("配置路径错误，期望: %s, 实际: %s", tt.configPath, manager.configPath)
			}
			if manager.schemaDir != tt.schemaDir {
				t.Errorf("模式目录错误，期望: %s, 实际: %s", tt.schemaDir, manager.schemaDir)
			}
			if manager.logger != tt.logger {
				t.Error("日志记录器设置错误")
			}
			if manager.watchers == nil {
				t.Error("监听器列表未初始化")
			}
			if manager.changeChannel == nil {
				t.Error("变更通道未初始化")
			}
		})
	}
}

// TestLoadConfig 测试配置加载
func TestLoadConfig(t *testing.T) {
	tests := []struct {
		name          string
		setupFunc     func(t *testing.T) (string, string)
		wantError     bool
		errorContains string
	}{
		{
			name: "成功加载有效配置",
			setupFunc: func(t *testing.T) (string, string) {
				configPath := createTestConfig(t)
				schemaDir := createTestSchemaDir(t)
				return configPath, schemaDir
			},
			wantError: false,
		},
		{
			name: "配置文件不存在",
			setupFunc: func(t *testing.T) (string, string) {
				return "/nonexistent/config.yaml", ""
			},
			wantError:     true,
			errorContains: "配置文件不存在",
		},
		{
			name: "配置文件格式错误",
			setupFunc: func(t *testing.T) (string, string) {
				configPath := createInvalidConfig(t)
				return configPath, ""
			},
			wantError:     true,
			errorContains: "解析配置文件失败",
		},
		{
			name: "模式目录不存在",
			setupFunc: func(t *testing.T) (string, string) {
				configPath := createTestConfig(t)
				return configPath, "/nonexistent/pdxp"
			},
			wantError:     true,
			errorContains: "PDXP模式目录不存在",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			configPath, schemaDir := tt.setupFunc(t)
			logger := NewMockLogger()
			manager := NewManager(configPath, schemaDir, logger)

			config, err := manager.LoadConfig()

			if tt.wantError {
				if err == nil {
					t.Errorf("期望出现错误，但没有错误")
				} else if tt.errorContains != "" && !contains(err.Error(), tt.errorContains) {
					t.Errorf("错误消息不匹配，期望包含: %s, 实际: %s", tt.errorContains, err.Error())
				}
			} else {
				if err != nil {
					t.Errorf("期望成功加载配置，但出现错误: %v", err)
				}
				if config == nil {
					t.Error("配置对象为nil")
				}
				if config != nil {
					// 验证配置内容
					if len(config.UDP) == 0 {
						t.Error("UDP配置列表为空")
					}
					if len(config.PDXP) == 0 {
						t.Error("PDXP配置列表为空")
					}
					if config.ConfigPath != configPath {
						t.Errorf("配置路径错误，期望: %s, 实际: %s", configPath, config.ConfigPath)
					}
				}
			}
		})
	}
}

// TestGetConfig 测试获取当前配置
func TestGetConfig(t *testing.T) {
	configPath := createTestConfig(t)
	schemaDir := createTestSchemaDir(t)
	logger := NewMockLogger()
	manager := NewManager(configPath, schemaDir, logger)

	// 配置未加载时
	config := manager.GetConfig()
	if config != nil {
		t.Error("配置未加载时应返回nil")
	}

	// 加载配置后
	loadedConfig, err := manager.LoadConfig()
	if err != nil {
		t.Fatalf("加载配置失败: %v", err)
	}

	currentConfig := manager.GetConfig()
	if currentConfig == nil {
		t.Error("加载配置后应返回配置对象")
	}
	if currentConfig != loadedConfig {
		t.Error("返回的配置对象不正确")
	}
}

// TestGetUDPConfigs 测试获取UDP配置列表
func TestGetUDPConfigs(t *testing.T) {
	configPath := createTestConfig(t)
	schemaDir := createTestSchemaDir(t)
	logger := NewMockLogger()
	manager := NewManager(configPath, schemaDir, logger)

	// 配置未加载时
	udpConfigs := manager.GetUDPConfigs()
	if len(udpConfigs) != 0 {
		t.Error("配置未加载时UDP配置列表应为空")
	}

	// 加载配置后
	_, err := manager.LoadConfig()
	if err != nil {
		t.Fatalf("加载配置失败: %v", err)
	}

	udpConfigs = manager.GetUDPConfigs()
	if len(udpConfigs) != 2 {
		t.Errorf("UDP配置数量错误，期望: 2, 实际: %d", len(udpConfigs))
	}

	// 验证第一个配置项
	firstConfig := udpConfigs[0]
	if firstConfig.Name != "test_tracking" {
		t.Errorf("第一个UDP配置名称错误，期望: test_tracking, 实际: %s", firstConfig.Name)
	}
	if firstConfig.Addr != "**********" {
		t.Errorf("第一个UDP配置地址错误，期望: **********, 实际: %s", firstConfig.Addr)
	}
	if firstConfig.Port != 2511 {
		t.Errorf("第一个UDP配置端口错误，期望: 2511, 实际: %d", firstConfig.Port)
	}
}

// TestGetPDXPFrameTypes 测试获取PDXP帧类型配置列表
func TestGetPDXPFrameTypes(t *testing.T) {
	configPath := createTestConfig(t)
	schemaDir := createTestSchemaDir(t)
	logger := NewMockLogger()
	manager := NewManager(configPath, schemaDir, logger)

	// 配置未加载时
	frameTypes := manager.GetPDXPFrameTypes()
	if len(frameTypes) != 0 {
		t.Error("配置未加载时PDXP帧类型列表应为空")
	}

	// 加载配置后
	_, err := manager.LoadConfig()
	if err != nil {
		t.Fatalf("加载配置失败: %v", err)
	}

	frameTypes = manager.GetPDXPFrameTypes()
	if len(frameTypes) != 2 {
		t.Errorf("PDXP帧类型数量错误，期望: 2, 实际: %d", len(frameTypes))
	}

	// 验证第一个帧类型
	firstFrame := frameTypes[0]
	if firstFrame.Name != "test_gx" {
		t.Errorf("第一个帧类型名称错误，期望: test_gx, 实际: %s", firstFrame.Name)
	}
	if firstFrame.Key != "GX" {
		t.Errorf("第一个帧类型键错误，期望: GX, 实际: %s", firstFrame.Key)
	}
	if firstFrame.Src != "test_tracking" {
		t.Errorf("第一个帧类型数据源错误，期望: test_tracking, 实际: %s", firstFrame.Src)
	}
	if len(firstFrame.BIDs) != 2 {
		t.Errorf("第一个帧类型BID数量错误，期望: 2, 实际: %d", len(firstFrame.BIDs))
	}
}

// TestGetPDXPSchema 测试获取PDXP模式
func TestGetPDXPSchema(t *testing.T) {
	configPath := createTestConfig(t)
	schemaDir := createTestSchemaDir(t)
	logger := NewMockLogger()
	manager := NewManager(configPath, schemaDir, logger)

	tests := []struct {
		name      string
		key       string
		wantError bool
		errorMsg  string
	}{
		{
			name:      "配置未加载",
			key:       "GX",
			wantError: true,
			errorMsg:  "配置尚未加载",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			schema, err := manager.GetPDXPSchema(tt.key)

			if tt.wantError {
				if err == nil {
					t.Error("期望出现错误，但没有错误")
				} else if !contains(err.Error(), tt.errorMsg) {
					t.Errorf("错误消息不匹配，期望包含: %s, 实际: %s", tt.errorMsg, err.Error())
				}
				if schema != nil {
					t.Error("错误情况下模式应为nil")
				}
			} else {
				if err != nil {
					t.Errorf("期望成功获取模式，但出现错误: %v", err)
				}
				if schema == nil {
					t.Error("成功情况下模式不应为nil")
				}
			}
		})
	}

	// 加载配置后测试
	_, err := manager.LoadConfig()
	if err != nil {
		t.Fatalf("加载配置失败: %v", err)
	}

	// 测试存在的模式
	schema, err := manager.GetPDXPSchema("GX")
	if err != nil {
		t.Errorf("获取存在的模式失败: %v", err)
	}
	if schema == nil {
		t.Error("存在的模式不应为nil")
	}
	if schema != nil && schema.Version != "3.0" {
		t.Errorf("模式版本错误，期望: 3.0, 实际: %s", schema.Version)
	}

	// 测试不存在的模式
	_, err = manager.GetPDXPSchema("NONEXISTENT")
	if err == nil {
		t.Error("获取不存在的模式应该报错")
	}
	if !contains(err.Error(), "未找到PDXP模式") {
		t.Errorf("错误消息不正确: %v", err)
	}
}

// TestReloadConfig 测试重新加载配置
func TestReloadConfig(t *testing.T) {
	configPath := createTestConfig(t)
	schemaDir := createTestSchemaDir(t)
	logger := NewMockLogger()
	manager := NewManager(configPath, schemaDir, logger)

	// 首次加载配置
	_, err := manager.LoadConfig()
	if err != nil {
		t.Fatalf("首次加载配置失败: %v", err)
	}

	// 添加配置监听器
	watcher := NewMockConfigWatcher()
	manager.AddWatcher(watcher)

	// 重新加载配置
	err = manager.ReloadConfig()
	if err != nil {
		t.Errorf("重新加载配置失败: %v", err)
	}

	// 验证监听器被调用
	configs := watcher.GetConfigs()
	if len(configs) != 1 {
		t.Errorf("监听器调用次数错误，期望: 1, 实际: %d", len(configs))
	}

	// 验证日志记录
	logs := logger.GetLogs()
	foundStart := false
	foundComplete := false
	for _, log := range logs {
		if log.Level == "info" && contains(log.Msg, "开始重新加载配置") {
			foundStart = true
		}
		if log.Level == "info" && contains(log.Msg, "配置重新加载完成") {
			foundComplete = true
		}
	}
	if !foundStart {
		t.Error("未找到重新加载开始日志")
	}
	if !foundComplete {
		t.Error("未找到重新加载完成日志")
	}
}

// TestAddRemoveWatcher 测试添加和移除监听器
func TestAddRemoveWatcher(t *testing.T) {
	configPath := createTestConfig(t)
	schemaDir := createTestSchemaDir(t)
	logger := NewMockLogger()
	manager := NewManager(configPath, schemaDir, logger)

	watcher1 := NewMockConfigWatcher()
	watcher2 := NewMockConfigWatcher()

	// 添加监听器
	manager.AddWatcher(watcher1)
	manager.AddWatcher(watcher2)

	if len(manager.watchers) != 2 {
		t.Errorf("监听器数量错误，期望: 2, 实际: %d", len(manager.watchers))
	}

	// 移除监听器
	manager.RemoveWatcher(watcher1)

	if len(manager.watchers) != 1 {
		t.Errorf("移除后监听器数量错误，期望: 1, 实际: %d", len(manager.watchers))
	}

	// 验证剩余的是watcher2
	if manager.watchers[0] != watcher2 {
		t.Error("剩余的监听器不正确")
	}

	// 移除不存在的监听器
	manager.RemoveWatcher(watcher1) // 再次移除
	if len(manager.watchers) != 1 {
		t.Errorf("移除不存在监听器后数量应不变，期望: 1, 实际: %d", len(manager.watchers))
	}
}

// contains 检查字符串是否包含子串
func contains(s, substr string) bool {
	return len(s) >= len(substr) && (s == substr ||
		(len(substr) > 0 && indexOf(s, substr) >= 0))
}

// indexOf 查找子串位置
func indexOf(s, substr string) int {
	for i := 0; i <= len(s)-len(substr); i++ {
		if s[i:i+len(substr)] == substr {
			return i
		}
	}
	return -1
}

// TestGetConfigStats 测试获取配置统计信息
func TestGetConfigStats(t *testing.T) {
	configPath := createTestConfig(t)
	schemaDir := createTestSchemaDir(t)
	logger := NewMockLogger()
	manager := NewManager(configPath, schemaDir, logger)

	// 配置未加载时
	stats := manager.GetConfigStats()
	if stats.ConfigPath != "" {
		t.Error("配置未加载时路径应为空")
	}
	if stats.TotalUDPEndpoints != 0 {
		t.Error("配置未加载时UDP端点数应为0")
	}

	// 加载配置后
	_, err := manager.LoadConfig()
	if err != nil {
		t.Fatalf("加载配置失败: %v", err)
	}

	stats = manager.GetConfigStats()
	if stats.ConfigPath != configPath {
		t.Errorf("配置路径错误，期望: %s, 实际: %s", configPath, stats.ConfigPath)
	}
	if stats.TotalUDPEndpoints != 2 {
		t.Errorf("UDP端点总数错误，期望: 2, 实际: %d", stats.TotalUDPEndpoints)
	}
	if stats.EnabledUDPEndpoints != 1 { // 只有一个启用
		t.Errorf("启用的UDP端点数错误，期望: 1, 实际: %d", stats.EnabledUDPEndpoints)
	}
	if stats.TotalPDXPFrames != 2 {
		t.Errorf("PDXP帧类型总数错误，期望: 2, 实际: %d", stats.TotalPDXPFrames)
	}
	if stats.EnabledPDXPFrames != 2 { // 两个都启用
		t.Errorf("启用的PDXP帧类型数错误，期望: 2, 实际: %d", stats.EnabledPDXPFrames)
	}
	if stats.TotalPDXPSchemas != 2 { // GX 和 ML
		t.Errorf("PDXP模式总数错误，期望: 2, 实际: %d", stats.TotalPDXPSchemas)
	}

	// 添加监听器并验证计数
	watcher := NewMockConfigWatcher()
	manager.AddWatcher(watcher)

	stats = manager.GetConfigStats()
	if stats.WatcherCount != 1 {
		t.Errorf("监听器数量错误，期望: 1, 实际: %d", stats.WatcherCount)
	}
}

// TestStartStopWatching 测试启动和停止配置文件监听
func TestStartStopWatching(t *testing.T) {
	configPath := createTestConfig(t)
	schemaDir := createTestSchemaDir(t)
	logger := NewMockLogger()
	manager := NewManager(configPath, schemaDir, logger)

	// 加载配置
	_, err := manager.LoadConfig()
	if err != nil {
		t.Fatalf("加载配置失败: %v", err)
	}

	// 启动监听
	err = manager.StartWatching()
	if err != nil {
		t.Errorf("启动监听失败: %v", err)
	}

	// 验证状态
	if !manager.isWatching {
		t.Error("监听状态应为true")
	}
	if manager.fsWatcher == nil {
		t.Error("文件系统监听器应已创建")
	}

	// 重复启动应该报错
	err = manager.StartWatching()
	if err == nil {
		t.Error("重复启动监听应该报错")
	}

	// 停止监听
	manager.StopWatching()

	// 验证状态
	if manager.isWatching {
		t.Error("监听状态应为false")
	}
	if manager.fsWatcher != nil {
		t.Error("文件系统监听器应已关闭")
	}

	// 重复停止不应出错
	manager.StopWatching() // 应该安全执行
}

// TestWatchConfigChanges 测试配置变更监听
func TestWatchConfigChanges(t *testing.T) {
	configPath := createTestConfig(t)
	schemaDir := createTestSchemaDir(t)
	logger := NewMockLogger()
	manager := NewManager(configPath, schemaDir, logger)

	// 加载配置
	_, err := manager.LoadConfig()
	if err != nil {
		t.Fatalf("加载配置失败: %v", err)
	}

	// 开始监听配置变更
	changeChan := manager.WatchConfigChanges()
	if changeChan == nil {
		t.Error("变更通道不应为nil")
	}

	// 验证监听已启动
	if !manager.isWatching {
		t.Error("配置监听应已启动")
	}

	// 监听启动事件
	ctx, cancel := context.WithTimeout(context.Background(), 2*time.Second)
	defer cancel()

	select {
	case event := <-changeChan:
		if event.Type != ConfigChangeTypeStarted {
			t.Errorf("期望启动事件，实际: %s", event.Type)
		}
	case <-ctx.Done():
		t.Error("未收到启动事件")
	}

	// 清理
	manager.StopWatching()
}

// TestValidateConfigFile 测试独立配置文件验证函数
func TestValidateConfigFile(t *testing.T) {
	tests := []struct {
		name          string
		setupFunc     func(t *testing.T) string
		wantError     bool
		errorContains string
	}{
		{
			name: "验证有效配置文件",
			setupFunc: func(t *testing.T) string {
				return createTestConfig(t)
			},
			wantError: false,
		},
		{
			name: "配置文件不存在",
			setupFunc: func(t *testing.T) string {
				return "/nonexistent/config.yaml"
			},
			wantError:     true,
			errorContains: "配置文件不存在",
		},
		{
			name: "无效的配置文件格式",
			setupFunc: func(t *testing.T) string {
				return createInvalidConfig(t)
			},
			wantError:     true,
			errorContains: "解析配置文件失败",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			configPath := tt.setupFunc(t)
			err := ValidateConfigFile(configPath)

			if tt.wantError {
				if err == nil {
					t.Error("期望出现错误，但没有错误")
				} else if tt.errorContains != "" && !contains(err.Error(), tt.errorContains) {
					t.Errorf("错误消息不匹配，期望包含: %s, 实际: %s", tt.errorContains, err.Error())
				}
			} else {
				if err != nil {
					t.Errorf("期望成功验证配置，但出现错误: %v", err)
				}
			}
		})
	}
}

// TestConcurrentAccess 测试并发访问安全性
func TestConcurrentAccess(t *testing.T) {
	configPath := createTestConfig(t)
	schemaDir := createTestSchemaDir(t)
	logger := NewMockLogger()
	manager := NewManager(configPath, schemaDir, logger)

	// 加载配置
	_, err := manager.LoadConfig()
	if err != nil {
		t.Fatalf("加载配置失败: %v", err)
	}

	const goroutineCount = 50
	const operationsPerGoroutine = 100

	var wg sync.WaitGroup
	wg.Add(goroutineCount)

	// 启动多个goroutine并发访问
	for i := 0; i < goroutineCount; i++ {
		go func(id int) {
			defer wg.Done()

			for j := 0; j < operationsPerGoroutine; j++ {
				// 并发读取操作
				_ = manager.GetConfig()
				_ = manager.GetUDPConfigs()
				_ = manager.GetPDXPFrameTypes()
				_ = manager.GetConfigStats()

				// 偶尔进行模式查询
				if j%10 == 0 {
					_, _ = manager.GetPDXPSchema("GX")
				}

				// 偶尔添加/移除监听器
				if j%20 == 0 {
					watcher := NewMockConfigWatcher()
					manager.AddWatcher(watcher)
					manager.RemoveWatcher(watcher)
				}
			}
		}(i)
	}

	// 等待所有goroutine完成
	done := make(chan struct{})
	go func() {
		wg.Wait()
		close(done)
	}()

	select {
	case <-done:
		// 测试通过
	case <-time.After(10 * time.Second):
		t.Error("并发测试超时")
	}
}

// TestConfigValidation 测试配置验证功能
func TestConfigValidation(t *testing.T) {
	tests := []struct {
		name          string
		configData    map[string]any
		wantError     bool
		errorContains string
	}{
		{
			name: "有效配置",
			configData: map[string]any{
				"udp": []map[string]any{
					{"name": "test1", "addr": "230.1.1.1", "port": 2511, "desc": "测试", "enable": true},
				},
				"pdxp": []map[string]any{
					{"name": "frame1", "key": "GX", "desc": "测试帧", "src": "test1", "enable": true, "bids": []uint32{0x100101}},
				},
			},
			wantError: false,
		},
		{
			name: "空UDP配置",
			configData: map[string]any{
				"udp":  []map[string]any{},
				"pdxp": []map[string]any{},
			},
			wantError:     true,
			errorContains: "UDP配置不能为空",
		},
		{
			name: "UDP端口无效",
			configData: map[string]any{
				"udp": []map[string]any{
					{"name": "test1", "addr": "230.1.1.1", "port": -1, "desc": "测试", "enable": true},
				},
				"pdxp": []map[string]any{},
			},
			wantError:     true,
			errorContains: "端口号必须在1-65535范围内",
		},
		{
			name: "UDP名称重复",
			configData: map[string]any{
				"udp": []map[string]any{
					{"name": "test1", "addr": "230.1.1.1", "port": 2511, "desc": "测试1", "enable": true},
					{"name": "test1", "addr": "230.1.1.2", "port": 2512, "desc": "测试2", "enable": true},
				},
				"pdxp": []map[string]any{},
			},
			wantError:     true,
			errorContains: "UDP端点名称'test1'重复",
		},
		{
			name: "PDXP源引用不存在",
			configData: map[string]any{
				"udp": []map[string]any{
					{"name": "test1", "addr": "230.1.1.1", "port": 2511, "desc": "测试", "enable": true},
				},
				"pdxp": []map[string]any{
					{"name": "frame1", "key": "GX", "desc": "测试帧", "src": "nonexistent", "enable": true, "bids": []uint32{0x100101}},
				},
			},
			wantError:     true,
			errorContains: "引用了不存在的数据源",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 创建临时配置文件
			tempDir := t.TempDir()
			configPath := filepath.Join(tempDir, "test_config.yaml")

			data, err := yaml.Marshal(tt.configData)
			if err != nil {
				t.Fatalf("创建测试配置失败: %v", err)
			}

			if err := os.WriteFile(configPath, data, 0644); err != nil {
				t.Fatalf("写入测试配置文件失败: %v", err)
			}

			// 创建空的模式目录
			schemaDir := filepath.Join(tempDir, "pdxp")
			if err := os.MkdirAll(schemaDir, 0755); err != nil {
				t.Fatalf("创建模式目录失败: %v", err)
			}

			logger := NewMockLogger()
			manager := NewManager(configPath, schemaDir, logger)

			_, err = manager.LoadConfig()

			if tt.wantError {
				if err == nil {
					t.Error("期望出现错误，但没有错误")
				} else if tt.errorContains != "" && !contains(err.Error(), tt.errorContains) {
					t.Errorf("错误消息不匹配，期望包含: %s, 实际: %s", tt.errorContains, err.Error())
				}
			} else {
				if err != nil {
					t.Errorf("期望成功加载配置，但出现错误: %v", err)
				}
			}
		})
	}
}

// TestFileWatchingWithRealChanges 测试真实文件变更的监听功能
func TestFileWatchingWithRealChanges(t *testing.T) {
	// 跳过长时间运行的测试（在CI环境中可能不稳定）
	if testing.Short() {
		t.Skip("跳过长时间运行的文件监听测试")
	}

	configPath := createTestConfig(t)
	schemaDir := createTestSchemaDir(t)
	logger := NewMockLogger()
	manager := NewManager(configPath, schemaDir, logger)

	// 加载初始配置
	_, err := manager.LoadConfig()
	if err != nil {
		t.Fatalf("加载初始配置失败: %v", err)
	}

	// 开始监听配置变更
	changeChan := manager.WatchConfigChanges()

	// 等待监听启动事件
	ctx, cancel := context.WithTimeout(context.Background(), 3*time.Second)
	defer cancel()

	// 接收启动事件
	select {
	case event := <-changeChan:
		if event.Type != ConfigChangeTypeStarted {
			t.Errorf("期望启动事件，实际: %s", event.Type)
		}
	case <-ctx.Done():
		t.Error("未收到启动事件")
	}

	// 修改配置文件
	modifiedConfig := map[string]any{
		"udp": []map[string]any{
			{
				"name":   "test_tracking_modified",
				"addr":   "**********",
				"port":   2511,
				"desc":   "修改后的测试追踪数据",
				"enable": true,
			},
		},
		"pdxp": []map[string]any{
			{
				"name":   "test_gx_modified",
				"key":    "GX",
				"desc":   "修改后的测试光学设备测量帧",
				"src":    "test_tracking_modified",
				"enable": true,
				"bids":   []uint32{0x00100101},
			},
		},
	}

	modifiedData, err := yaml.Marshal(modifiedConfig)
	if err != nil {
		t.Fatalf("创建修改后的配置失败: %v", err)
	}

	// 写入修改后的配置
	if err := os.WriteFile(configPath, modifiedData, 0644); err != nil {
		t.Fatalf("写入修改后的配置失败: %v", err)
	}

	// 等待文件变更事件
	changeCtx, changeCancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer changeCancel()

	select {
	case event := <-changeChan:
		if event.Type != ConfigChangeTypeReload {
			t.Errorf("期望重载事件，实际: %s", event.Type)
		}
		if event.Config == nil {
			t.Error("变更事件应包含新配置")
		}
		if event.Error != nil {
			t.Errorf("变更事件不应包含错误: %v", event.Error)
		}
	case <-changeCtx.Done():
		t.Error("未收到配置变更事件")
	}

	// 验证配置已更新
	currentConfig := manager.GetConfig()
	if currentConfig == nil {
		t.Fatal("当前配置不应为nil")
	}

	if len(currentConfig.UDP) != 1 {
		t.Errorf("UDP配置数量错误，期望: 1, 实际: %d", len(currentConfig.UDP))
	}

	if currentConfig.UDP[0].Name != "test_tracking_modified" {
		t.Errorf("UDP配置名称未更新，期望: test_tracking_modified, 实际: %s", currentConfig.UDP[0].Name)
	}

	// 清理
	manager.StopWatching()

	// 等待停止事件（可选，因为停止事件可能不会总是收到）
	stopCtx, stopCancel := context.WithTimeout(context.Background(), 1*time.Second)
	defer stopCancel()

	select {
	case event := <-changeChan:
		if event.Type != ConfigChangeTypeStopped {
			t.Logf("收到非停止事件: %s，这是正常的", event.Type)
		} else {
			t.Logf("成功收到停止事件")
		}
	case <-stopCtx.Done():
		t.Logf("未收到停止事件，这是正常的")
	}
}

// TestErrorHandling 测试错误处理
func TestErrorHandling(t *testing.T) {
	// 测试不存在的配置文件
	logger := NewMockLogger()
	manager := NewManager("/nonexistent/config.yaml", "/nonexistent/pdxp", logger)

	_, err := manager.LoadConfig()
	if err == nil {
		t.Error("加载不存在的配置文件应该出错")
	}

	// 测试获取未加载配置的方法
	udpConfigs := manager.GetUDPConfigs()
	if len(udpConfigs) != 0 {
		t.Error("未加载配置时应返回空列表")
	}

	frameTypes := manager.GetPDXPFrameTypes()
	if len(frameTypes) != 0 {
		t.Error("未加载配置时应返回空列表")
	}

	_, err = manager.GetPDXPSchema("GX")
	if err == nil {
		t.Error("未加载配置时获取模式应该出错")
	}

	stats := manager.GetConfigStats()
	if stats.ConfigPath != "" {
		t.Error("未加载配置时统计信息应为空")
	}

	// 测试重载未加载的配置
	err = manager.ReloadConfig()
	if err == nil {
		t.Error("重载未加载的配置应该出错")
	}
}

// BenchmarkLoadConfig 配置加载性能基准测试
func BenchmarkLoadConfig(b *testing.B) {
	configPath := createBenchmarkConfig(b)
	schemaDir := createBenchmarkSchemaDir(b)
	logger := NewMockLogger()

	b.ResetTimer()

	for i := 0; i < b.N; i++ {
		manager := NewManager(configPath, schemaDir, logger)
		_, err := manager.LoadConfig()
		if err != nil {
			b.Fatalf("配置加载失败: %v", err)
		}
	}
}

// BenchmarkConcurrentRead 并发读取性能基准测试
func BenchmarkConcurrentRead(b *testing.B) {
	configPath := createBenchmarkConfig(b)
	schemaDir := createBenchmarkSchemaDir(b)
	logger := NewMockLogger()
	manager := NewManager(configPath, schemaDir, logger)

	_, err := manager.LoadConfig()
	if err != nil {
		b.Fatalf("配置加载失败: %v", err)
	}

	b.ResetTimer()

	b.RunParallel(func(pb *testing.PB) {
		for pb.Next() {
			_ = manager.GetConfig()
			_ = manager.GetUDPConfigs()
			_ = manager.GetPDXPFrameTypes()
			_, _ = manager.GetPDXPSchema("GX")
		}
	})
}

// createBenchmarkConfig 创建性能测试用的配置文件
func createBenchmarkConfig(b *testing.B) string {
	b.Helper()

	tempDir := b.TempDir()
	configPath := filepath.Join(tempDir, "benchmark_config.yaml")

	// 创建较大的配置文件用于性能测试
	var udpConfigs []map[string]any
	var pdxpConfigs []map[string]any

	for i := 0; i < 100; i++ {
		udpConfigs = append(udpConfigs, map[string]any{
			"name":   fmt.Sprintf("udp_%d", i),
			"addr":   fmt.Sprintf("230.25.%d.%d", i/255, i%255+1),
			"port":   2500 + i,
			"desc":   fmt.Sprintf("性能测试UDP端点 %d", i),
			"enable": i%2 == 0,
		})

		pdxpConfigs = append(pdxpConfigs, map[string]any{
			"name":   fmt.Sprintf("frame_%d", i),
			"key":    fmt.Sprintf("KEY_%d", i%10),
			"desc":   fmt.Sprintf("性能测试帧类型 %d", i),
			"src":    fmt.Sprintf("udp_%d", i%50),
			"enable": i%3 != 0,
			"bids":   []uint32{uint32(0x100000 + i)},
		})
	}

	config := map[string]any{
		"udp":  udpConfigs,
		"pdxp": pdxpConfigs,
	}

	data, err := yaml.Marshal(config)
	if err != nil {
		b.Fatalf("创建性能测试配置失败: %v", err)
	}

	if err := os.WriteFile(configPath, data, 0644); err != nil {
		b.Fatalf("写入性能测试配置文件失败: %v", err)
	}

	return configPath
}

// createBenchmarkSchemaDir 创建性能测试用的模式目录
func createBenchmarkSchemaDir(b *testing.B) string {
	b.Helper()

	tempDir := b.TempDir()
	schemaDir := filepath.Join(tempDir, "pdxp")
	if err := os.MkdirAll(schemaDir, 0755); err != nil {
		b.Fatalf("创建性能测试模式目录失败: %v", err)
	}

	// 创建多个模式文件
	for i := 0; i < 10; i++ {
		keyName := fmt.Sprintf("KEY_%d", i)
		schema := map[string]any{
			"version": "3.0",
			keyName: map[string]any{
				"desc":  fmt.Sprintf("性能测试模式 %d", i),
				"class": "block",
				"type":  "dynamic",
				"members": []map[string]any{
					{
						"name":  "T",
						"desc":  "时间戳",
						"class": "field",
						"type":  "uint32",
						"func":  "time",
					},
					{
						"name":  "Data",
						"desc":  "数据",
						"class": "field",
						"type":  "uint64",
						"func":  "identity",
					},
				},
			},
		}

		data, err := yaml.Marshal(schema)
		if err != nil {
			b.Fatalf("创建性能测试模式失败: %v", err)
		}

		schemaPath := filepath.Join(schemaDir, fmt.Sprintf("%s.yaml", keyName))
		if err := os.WriteFile(schemaPath, data, 0644); err != nil {
			b.Fatalf("写入性能测试模式文件失败: %v", err)
		}
	}

	return schemaDir
}
