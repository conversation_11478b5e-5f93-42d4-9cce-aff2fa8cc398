# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 项目概述

Aquila PDXP Backend 是一个高性能的 UDP 组播监听与 PDXP 协议解析服务，用于实时监听指定的 UDP 组播地址，使用线程池并发解析 PDXP 数据，维护程序状态，并通过 HTTP SSE 推送状态更新和 REST API 提供历史数据查询功能。

## 开发环境命令

### 构建和运行

```bash
# 构建主程序
go build -o bin/aquila cmd/aquila/my.go

# 运行程序 (当前为hello world版本)
go run cmd/aquila/my.go

# 构建生产版本
go build -ldflags="-s -w" -o bin/aquila cmd/aquila/my.go
```

### 测试

```bash
# 运行所有测试
go test ./...

# 运行测试并显示详细输出
go test -v ./...

# 运行测试并生成覆盖率报告
go test -cover ./...
go test -coverprofile=coverage.out ./...

# 查看覆盖率详情
go tool cover -html=coverage.out

# 运行特定包的测试
go test -v ./internal/config
go test -v ./internal/types
```

### 代码质量

```bash
# 格式化代码
go fmt ./...

# 代码检查
go vet ./...

# 下载依赖
go mod download

# 整理依赖
go mod tidy

# 检查依赖漏洞
go list -json -deps ./... | go-mod-explore
```

### 性能分析

```bash
# CPU 性能分析
go test -cpuprofile cpu.prof -bench .

# 内存性能分析  
go test -memprofile mem.prof -bench .

# 基准测试
go test -bench=. -benchmem ./...
```

## 代码架构

### 目录结构

- `cmd/aquila/` - 主程序入口点（目前包含 my.go 的 hello world 版本）
- `internal/` - 内部业务逻辑包，不对外暴露
  - `internal/types/` - 核心数据类型定义，包括完整的配置结构
  - `internal/config/` - 配置管理器，支持热重载
- `config/` - 配置文件
  - `config/config.yaml` - 主配置文件，包含22个UDP组播地址和88种PDXP帧类型
  - `config/pdxp/` - PDXP协议结构定义文件

### 核心配置系统

项目已实现完整的配置系统，主要组件：

- **AppConfig**: 完整的应用配置结构 (`internal/types/config.go`)
- **UDP组播配置**: 支持22个UDP组播地址的并发监听
- **PDXP协议配置**: 支持88种帧类型，基于BID字段动态识别和解析
- **线程池配置**: PDXP解析线程池和UDP监听线程池
- **Redis时间序列**: 高性能数据缓存和状态管理
- **HTTP/SSE服务**: REST API和实时状态推送
- **监控指标**: Prometheus指标收集
- **结构化日志**: 基于slog的中文日志输出

### 实现任务进度

基于 `tasks.md` 的实现计划，已完成：

- ✅ 任务1-6: 基础设施和配置管理
  - Go模块依赖已配置
  - 项目目录结构已建立
  - 核心数据类型已定义
  - 配置加载器已实现
  - 配置热重载功能已完成
  - 单元测试已创建

待实现组件（按优先级）：

- UDP监听器和集群管理
- PDXP协议解析引擎
- 线程池和并发处理
- Redis存储和状态管理  
- HTTP服务和REST API
- SSE实时推送服务
- 系统监控和日志
- 主程序集成

### 关键设计模式

- **配置驱动**: 基于YAML配置的动态系统初始化
- **并发安全**: 使用sync.RWMutex保护共享状态
- **错误处理**: 中文错误信息，结构化错误类型
- **内存优化**: 使用sync.Pool对象池和worker pool
- **时间序列**: Redis Timeseries专用于数据缓存
- **热重载**: 文件监控实现配置动态更新

### 性能目标

- UDP数据接收：>= 10,000 packets/sec per address
- PDXP协议解析：>= 80,000 frames/sec  
- SSE并发连接：>= 100 connections
- 端到端延迟：< 200ms (P99)
- 单元测试覆盖率：>= 80%

## 开发注意事项

### 代码规范

- 严格遵循 Go 官方代码规范
- 所有函数和结构体必须有中文注释
- 错误信息使用中文
- 单元测试覆盖率必须 >= 80%

### 依赖管理

项目使用 Go 1.25.0，主要依赖：

- Echo v4.x (HTTP框架)
- Redis client v9.x (数据缓存)
- YAML v3 (配置解析)
- Prometheus client (监控指标)
- golang.org/x/net (网络编程)
- golang.org/x/sync (并发控制)

### 并发安全

所有共享状态操作必须使用适当的同步机制：

- 配置热重载使用读写锁
- 状态管理使用并发安全的数据结构
- 线程池使用worker池模式
- Redis操作使用连接池

### 测试策略

- 单元测试：每个包都有对应的_test.go文件
- 集成测试：tests/目录包含端到端测试
- 基准测试：性能关键组件包含benchmark
- 测试数据：使用testdata/目录存放测试文件

### 监控和日志

- 使用结构化日志(slog)输出中文日志信息
- Prometheus指标收集系统运行状态
- 健康检查接口支持服务监控
- 错误日志包含上下文信息便于调试
