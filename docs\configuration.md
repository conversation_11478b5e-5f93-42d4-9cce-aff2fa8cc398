# Aquila PDXP Backend 配置文档

## 概述

本文档详细说明了 Aquila PDXP Backend 的完整配置结构。配置文件采用 YAML 格式，包含了系统运行所需的所有组件配置。

## 配置文件结构

### 1. UDP 组播配置 (`udp`)

UDP 组播监听配置，支持多个组播地址并发监听。

```yaml
udp:
- name: tracking_primary          # 端点唯一名称
  addr: **********               # 组播地址
  port: 2511                     # 监听端口
  desc: 主平面外测数据            # 描述信息
  enable: true                   # 是否启用
```

**字段说明：**

- `name`: 端点的唯一标识符，不可重复
- `addr`: IPv4 组播地址
- `port`: UDP 监听端口 (1-65535)
- `desc`: 端点描述信息
- `enable`: 是否启用该端点

### 2. PDXP 协议配置 (`pdxp`)

PDXP 帧类型配置，基于 BID 的帧识别和解析。

**重要说明**：所有PDXP帧都包含统一的32字节标准帧头，该帧头结构定义在 `config/pdxp/Header.yaml` 文件中，配置管理器会自动加载，无需在帧类型配置中单独引用。

**动态Header解析**：Header采用与数据域相同的动态解析机制，支持字段的灵活配置和扩展。通过 `PDXPPacket.GetBID()` 和 `PDXPPacket.GetTimestamp()` 方法可以便捷访问Header信息。

```yaml
pdxp:
- name: gx_pri                    # 帧类型唯一名称
  key: GX                        # 帧结构定义键名
  desc: 一平面光学设备测量帧       # 描述信息
  src: tracking_primary          # 数据源名称（对应UDP端点）
  enable: true                   # 是否启用
  bids:                          # BID 列表
  - 0x00100101
  - 0x00100102
```

**字段说明：**

- `name`: 帧类型的唯一标识符
- `key`: 对应的帧结构定义键名
- `desc`: 帧类型描述信息
- `src`: 数据源名称，必须对应已定义的 UDP 端点
- `enable`: 是否启用该帧类型
- `bids`: 业务标识符列表，用于帧识别

### 3. 线程池配置 (`thread_pool`)

高性能并发处理的线程池配置。

```yaml
thread_pool:
  # PDXP解析线程池
  pdxp_workers: 16               # PDXP解析工作线程数
  pdxp_queue_size: 10000         # PDXP解析队列大小
  pdxp_timeout: 5s               # PDXP解析超时时间
  
  # UDP监听线程池
  udp_workers: 8                 # UDP监听工作线程数
  udp_buffer_size: 65536         # UDP缓冲区大小
  udp_timeout: 1s                # UDP读取超时时间
  
  # 通用配置
  max_idle_time: 30s             # 最大空闲时间
  graceful_stop: 10s             # 优雅停止超时时间
```

**性能建议：**

- `pdxp_workers`: 建议设置为 CPU 核心数的 2-4 倍
- `udp_workers`: 建议设置为 UDP 端点数量
- `pdxp_queue_size`: 根据数据量调整，避免内存溢出

### 4. Redis 配置 (`redis`)

Redis 时间序列数据存储配置。

```yaml
redis:
  # 连接配置
  host: "localhost"              # Redis主机地址
  port: 6379                     # Redis端口
  password: ""                   # Redis密码
  db: 0                          # Redis数据库编号
  
  # 连接池配置
  pool_size: 20                  # 连接池大小
  min_idle_conns: 5              # 最小空闲连接数
  max_retries: 3                 # 最大重试次数
  dial_timeout: 5s               # 连接超时时间
  read_timeout: 3s               # 读取超时时间
  write_timeout: 3s              # 写入超时时间
  
  # 时间序列配置
  ts_retention: 168h             # 时间序列数据保留时间(7天)
  ts_batch_size: 1000            # 批量写入大小
  ts_batch_timeout: 100ms        # 批量写入超时时间
  ts_key_prefix: "aquila:ts:"    # 时间序列键前缀
```

**注意事项：**

- 需要 Redis 8.0+ 版本支持 TimeSeries 模块
- `ts_retention` 根据存储需求调整
- `pool_size` 应根据并发需求设置

### 5. HTTP 服务配置 (`http`)

REST API 和 Web 服务配置。

```yaml
http:
  # 服务器配置
  host: "0.0.0.0"               # 监听地址
  port: 8080                    # 监听端口
  read_timeout: 30s             # 读取超时时间
  write_timeout: 30s            # 写入超时时间
  idle_timeout: 120s            # 空闲超时时间
  
  # CORS配置
  cors:
    enabled: true               # 是否启用CORS
    allow_origins: ["*"]        # 允许的源
    allow_methods:              # 允许的方法
      - "GET"
      - "POST"
      - "PUT"
      - "DELETE"
      - "OPTIONS"
    allow_headers:              # 允许的头部
      - "Content-Type"
      - "Authorization"
      - "X-Requested-With"
    max_age: 86400              # 预检请求缓存时间(24小时)
  
  # API配置
  api:
    default_page_size: 100      # 默认分页大小
    max_page_size: 1000         # 最大分页大小
    max_time_range: 24h         # 最大时间范围
    default_time_span: 1h       # 默认时间跨度
    
    # 限流配置
    rate_limit:
      enabled: true             # 是否启用限流
      rps: 100.0                # 每秒请求数限制
      burst: 200                # 突发请求数限制
```

### 6. SSE 配置 (`sse`)

Server-Sent Events 实时推送配置。

```yaml
sse:
  # 连接配置
  max_connections: 100          # 最大并发连接数
  buffer_size: 1000             # 事件缓冲区大小
  ping_interval: 30s            # 心跳间隔
  write_timeout: 10s            # 写入超时时间
  
  # 推送配置
  push_frequency: 100ms         # 推送频率(10Hz)
  batch_size: 50                # 批量推送大小
  filter_enabled: true          # 是否启用过滤
  
  # 重连配置
  retry_interval: 5s            # 重连间隔
  max_retries: 3                # 最大重试次数
```

**性能目标：**

- 支持 >= 100 并发连接
- 推送延迟 < 100ms
- 推送频率可配置 1-10Hz

### 7. 监控指标配置 (`metrics`)

Prometheus 监控指标配置。

```yaml
metrics:
  # Prometheus配置
  enabled: true                 # 是否启用指标收集
  path: "/metrics"              # 指标路径
  namespace: "aquila"           # 指标命名空间
  subsystem: "pdxp_backend"     # 指标子系统
  
  # 收集配置
  collect_interval: 15s         # 收集间隔
  histogram_buckets:            # 直方图桶配置
    - 0.001
    - 0.005
    - 0.01
    - 0.025
    - 0.05
    - 0.1
    - 0.25
    - 0.5
    - 1.0
    - 2.5
    - 5.0
    - 10.0
  
  # 系统指标
  system_metrics:
    cpu: true                   # 是否收集CPU指标
    memory: true                # 是否收集内存指标
    disk: true                  # 是否收集磁盘指标
    network: true               # 是否收集网络指标
    goroutine: true             # 是否收集Goroutine指标
```

### 8. 日志配置 (`logging`)

结构化日志配置。

```yaml
logging:
  # 基础配置
  level: "info"                 # 日志级别 (debug/info/warn/error)
  format: "json"                # 日志格式 (json/text)
  
  # 输出配置
  output:                       # 输出目标
    - "stdout"
    - "file"
  filename: "logs/aquila.log"   # 日志文件名
  
  # 轮转配置
  rotation:
    enabled: true               # 是否启用轮转
    max_size: 100               # 最大文件大小(MB)
    max_age: 30                 # 最大保留天数
    max_backups: 10             # 最大备份文件数
    compress: true              # 是否压缩
  
  # 结构化日志配置
  structured:
    enabled: true               # 是否启用结构化日志
    fields:                     # 包含的字段
      - "timestamp"
      - "level"
      - "message"
      - "component"
      - "trace_id"
    exclude_keys:               # 排除的键
      - "password"
      - "secret"
    time_format: "2006-01-02T15:04:05.000Z07:00" # 时间格式
```

### 9. 系统配置 (`system`)

系统级配置和性能调优。

```yaml
system:
  # 应用信息
  name: "Aquila PDXP Backend"   # 应用名称
  version: "1.0.0"              # 应用版本
  environment: "production"     # 运行环境 (development/testing/production)
  
  # 性能配置
  max_procs: 0                  # 最大处理器数 (0=自动检测)
  gc_percent: 100               # GC百分比
  memory_limit: **********      # 内存限制(8GB)
  
  # 健康检查配置
  health_check:
    enabled: true               # 是否启用健康检查
    path: "/health"             # 健康检查路径
    interval: 30s               # 检查间隔
    timeout: 5s                 # 检查超时时间
  
  # 优雅关闭配置
  graceful_shutdown:
    timeout: 30s                # 关闭超时时间
    wait_signals:               # 等待的信号
      - "SIGTERM"
      - "SIGINT"
```

## 性能目标

根据设计文档，系统应满足以下性能要求：

- **UDP数据接收**: >= 10,000 packets/sec 单个组播地址
- **PDXP协议解析**: >= 80,000 frames/sec 线程池解析
- **HTTP SSE推送**: >= 100 并发连接
- **端到端延迟**: < 50ms (P99)
- **REST API响应**: < 100ms (P95)
- **状态推送延迟**: < 100ms

## 配置验证

系统启动时会自动验证配置文件的有效性，包括：

1. **语法检查**: YAML 格式正确性
2. **字段验证**: 必填字段和数据类型检查
3. **引用检查**: PDXP 帧类型的数据源引用验证
4. **范围检查**: 端口号、超时时间等数值范围验证
5. **重复检查**: 名称唯一性验证

## 最佳实践

1. **开发环境**: 降低线程数和缓冲区大小，启用详细日志
2. **测试环境**: 使用生产环境相似的配置进行性能测试
3. **生产环境**: 根据实际负载调优线程池和缓冲区配置
4. **监控**: 启用所有监控指标，定期检查系统性能
5. **日志**: 生产环境使用 JSON 格式，便于日志分析

## PDXP协议规范

PDXP协议的详细规范说明请参考 [PDXP协议规范文档](pdxp_specification.md)，包含：

- 协议版本信息和数据包结构定义
- 包头字段详细说明和BID映射表
- 数据类型、转换函数和帧结构定义规范
- 解析流程、错误处理和性能优化指导

实际的帧结构定义文件位于 `config/pdxp/` 目录下，采用动态配置方式。

## 相关文档

- [设计文档](.augment/rules/design.md)
- [产品文档](.augment/rules/product.md)
- [技术架构](.augment/rules/tech.md)
- [任务列表](tasks.md)
- [PDXP协议规范](pdxp_specification.md)
- [动态Header设计](dynamic_header_design.md)
