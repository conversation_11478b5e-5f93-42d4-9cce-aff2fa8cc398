// Package udp UDP监听器集群管理器实现
// 支持多个UDP组播地址的并发监听和统一管理
package udp

import (
	"context"
	"fmt"
	"log/slog"
	"reflect"
	"sync"
	"sync/atomic"
	"time"

	"aquila/internal/types"
)

// UDPListenerCluster UDP监听器集群接口
type UDPListenerCluster interface {
	// Start 启动所有监听器
	Start(ctx context.Context, config *types.UDPConfig) error

	// Stop 停止所有监听器
	Stop() error

	// GetAllPacketChannels 获取所有数据包通道的合并通道
	GetAllPacketChannels() <-chan *PDXPPacket

	// GetListener 根据名称获取特定监听器
	GetListener(name string) UDPListener

	// GetListeners 获取所有监听器
	GetListeners() map[string]UDPListener

	// GetClusterStats 获取集群统计信息
	GetClusterStats() *UDPClusterStats

	// IsHealthy 检查集群健康状态
	IsHealthy() bool

	// AddListener 动态添加监听器
	AddListener(endpoint types.UDPEndpoint) error

	// RemoveListener 动态移除监听器
	RemoveListener(name string) error

	// UpdateConfig 动态更新配置
	UpdateConfig(config *types.UDPConfig) error
}

// UDPClusterStats UDP集群统计信息
type UDPClusterStats struct {
	TotalListeners   int                          `json:"total_listeners"`   // 总监听器数
	ActiveListeners  int                          `json:"active_listeners"`  // 活跃监听器数
	HealthyListeners int                          `json:"healthy_listeners"` // 健康监听器数
	TotalPackets     int64                        `json:"total_packets"`     // 总接收包数
	ValidPackets     int64                        `json:"valid_packets"`     // 有效包数
	InvalidPackets   int64                        `json:"invalid_packets"`   // 无效包数
	TotalBytes       int64                        `json:"total_bytes"`       // 总接收字节数
	AvgPacketRate    float64                      `json:"avg_packet_rate"`   // 平均包速率
	AvgByteRate      float64                      `json:"avg_byte_rate"`     // 平均字节速率
	TotalErrors      int64                        `json:"total_errors"`      // 总错误数
	ClusterHealth    HealthStatus                 `json:"cluster_health"`    // 集群健康状态
	ListenerStats    map[string]*UDPListenerStats `json:"listener_stats"`    // 各监听器统计
	StartTime        time.Time                    `json:"start_time"`        // 集群启动时间
	LastUpdate       time.Time                    `json:"last_update"`       // 最后更新时间
	mutex            sync.RWMutex                 `json:"-"`                 // 读写锁
}

// udpListenerCluster UDP监听器集群实现
type udpListenerCluster struct {
	listeners      map[string]UDPListener
	mergedChan     chan *PDXPPacket
	stats          *UDPClusterStats
	logger         *slog.Logger
	ctx            context.Context
	cancel         context.CancelFunc
	wg             sync.WaitGroup
	mutex          sync.RWMutex
	running        int32
	bufferSize     int
	mergedChanSize int
}

// NewUDPListenerCluster 创建新的UDP监听器集群
func NewUDPListenerCluster(logger *slog.Logger, bufferSize, mergedChanSize int) UDPListenerCluster {
	if bufferSize <= 0 {
		bufferSize = DefaultBufferSize
	}
	if mergedChanSize <= 0 {
		mergedChanSize = DefaultPacketChannelSize * 10 // 默认为单个通道的10倍
	}

	return &udpListenerCluster{
		listeners:      make(map[string]UDPListener),
		mergedChan:     make(chan *PDXPPacket, mergedChanSize),
		bufferSize:     bufferSize,
		mergedChanSize: mergedChanSize,
		logger:         logger.With("component", "udp-cluster"),
		stats: &UDPClusterStats{
			ListenerStats: make(map[string]*UDPListenerStats),
			StartTime:     time.Now(),
			ClusterHealth: HealthStatusDown,
		},
	}
}

// Start 启动所有监听器
func (c *udpListenerCluster) Start(ctx context.Context, config *types.UDPConfig) error {
	if !atomic.CompareAndSwapInt32(&c.running, 0, 1) {
		return types.NewNetworkError(
			types.ErrorCodeUDPListenFailed,
			"UDP监听器集群已在运行",
		)
	}

	c.ctx, c.cancel = context.WithCancel(ctx)
	c.logger.Info("启动UDP监听器集群", "端点数量", len(config.UDP))

	// 验证配置
	if err := config.Validate(); err != nil {
		atomic.StoreInt32(&c.running, 0)
		return types.NewConfigError(
			types.ErrorCodeConfigValidationFailed,
			fmt.Sprintf("UDP配置验证失败: %v", err),
		)
	}

	// 创建并启动所有启用的监听器
	enabledEndpoints := config.GetEnabledEndpoints()
	if len(enabledEndpoints) == 0 {
		atomic.StoreInt32(&c.running, 0)
		return types.NewConfigError(
			types.ErrorCodeConfigValidationFailed,
			"没有启用的UDP端点",
		)
	}

	c.mutex.Lock()
	for _, endpoint := range enabledEndpoints {
		listener := NewUDPListener(endpoint, c.logger, c.bufferSize)
		c.listeners[endpoint.Name] = listener

		if err := listener.Start(c.ctx); err != nil {
			c.mutex.Unlock()
			c.Stop() // 清理已启动的监听器
			return types.NewNetworkError(
				types.ErrorCodeUDPListenFailed,
				fmt.Sprintf("启动UDP监听器'%s'失败", endpoint.Name),
			).WithCause(err)
		}

		c.logger.Info("UDP监听器启动成功",
			"端点", endpoint.Name,
			"地址", fmt.Sprintf("%s:%d", endpoint.Addr, endpoint.Port))
	}
	c.mutex.Unlock()

	// 启动数据包合并器
	c.wg.Add(1)
	go c.packetMerger()

	// 启动统计更新器
	c.wg.Add(1)
	go c.statsUpdater()

	// 等待监听器完全启动后初始化统计信息
	time.Sleep(50 * time.Millisecond)
	c.updateClusterStats()

	c.logger.Info("UDP监听器集群启动完成",
		"总监听器数", len(c.listeners),
		"启用监听器数", len(enabledEndpoints))

	return nil
}

// Stop 停止所有监听器
func (c *udpListenerCluster) Stop() error {
	if !atomic.CompareAndSwapInt32(&c.running, 1, 0) {
		return nil // 已经停止
	}

	c.logger.Info("停止UDP监听器集群")

	// 取消上下文
	if c.cancel != nil {
		c.cancel()
	}

	// 停止所有监听器
	c.mutex.Lock()
	for name, listener := range c.listeners {
		if err := listener.Stop(); err != nil {
			c.logger.Error("停止UDP监听器失败", "端点", name, "错误", err.Error())
		} else {
			c.logger.Info("UDP监听器已停止", "端点", name)
		}
	}
	c.listeners = make(map[string]UDPListener) // 清空监听器映射
	c.mutex.Unlock()

	// 等待goroutines结束
	c.wg.Wait()

	// 关闭合并通道
	close(c.mergedChan)

	c.logger.Info("UDP监听器集群已停止")
	return nil
}

// GetAllPacketChannels 获取所有数据包通道的合并通道
func (c *udpListenerCluster) GetAllPacketChannels() <-chan *PDXPPacket {
	return c.mergedChan
}

// GetListener 根据名称获取特定监听器
func (c *udpListenerCluster) GetListener(name string) UDPListener {
	c.mutex.RLock()
	defer c.mutex.RUnlock()
	return c.listeners[name]
}

// GetListeners 获取所有监听器
func (c *udpListenerCluster) GetListeners() map[string]UDPListener {
	c.mutex.RLock()
	defer c.mutex.RUnlock()

	// 返回副本避免并发访问问题
	result := make(map[string]UDPListener, len(c.listeners))
	for name, listener := range c.listeners {
		result[name] = listener
	}
	return result
}

// GetClusterStats 获取集群统计信息
func (c *udpListenerCluster) GetClusterStats() *UDPClusterStats {
	c.stats.mutex.RLock()
	defer c.stats.mutex.RUnlock()

	// 返回副本以避免并发访问问题，但不复制mutex
	statsCopy := UDPClusterStats{
		TotalListeners:   c.stats.TotalListeners,
		ActiveListeners:  c.stats.ActiveListeners,
		HealthyListeners: c.stats.HealthyListeners,
		TotalPackets:     c.stats.TotalPackets,
		ValidPackets:     c.stats.ValidPackets,
		InvalidPackets:   c.stats.InvalidPackets,
		TotalBytes:       c.stats.TotalBytes,
		AvgPacketRate:    c.stats.AvgPacketRate,
		AvgByteRate:      c.stats.AvgByteRate,
		TotalErrors:      c.stats.TotalErrors,
		ClusterHealth:    c.stats.ClusterHealth,
		StartTime:        c.stats.StartTime,
		LastUpdate:       c.stats.LastUpdate,
		ListenerStats:    make(map[string]*UDPListenerStats),
	}
	for name, stats := range c.stats.ListenerStats {
		statsCopy.ListenerStats[name] = stats
	}
	return &statsCopy
}

// IsHealthy 检查集群健康状态
func (c *udpListenerCluster) IsHealthy() bool {
	stats := c.GetClusterStats()
	return stats.ClusterHealth == HealthStatusHealthy || stats.ClusterHealth == HealthStatusDegraded
}

// AddListener 动态添加监听器
func (c *udpListenerCluster) AddListener(endpoint types.UDPEndpoint) error {
	if atomic.LoadInt32(&c.running) != 1 {
		return types.NewSystemError(
			types.ErrorCodeServiceUnavailable,
			"集群未运行，无法添加监听器",
		)
	}

	c.mutex.Lock()
	defer c.mutex.Unlock()

	// 检查是否已存在
	if _, exists := c.listeners[endpoint.Name]; exists {
		return types.NewConfigError(
			types.ErrorCodeConfigValidationFailed,
			fmt.Sprintf("监听器'%s'已存在", endpoint.Name),
		)
	}

	// 创建并启动新监听器
	listener := NewUDPListener(endpoint, c.logger, c.bufferSize)
	if err := listener.Start(c.ctx); err != nil {
		return types.NewNetworkError(
			types.ErrorCodeUDPListenFailed,
			fmt.Sprintf("启动新监听器'%s'失败", endpoint.Name),
		).WithCause(err)
	}

	c.listeners[endpoint.Name] = listener
	c.logger.Info("动态添加UDP监听器成功",
		"端点", endpoint.Name,
		"地址", fmt.Sprintf("%s:%d", endpoint.Addr, endpoint.Port))

	return nil
}

// RemoveListener 动态移除监听器
func (c *udpListenerCluster) RemoveListener(name string) error {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	listener, exists := c.listeners[name]
	if !exists {
		return types.NewConfigError(
			types.ErrorCodeConfigValidationFailed,
			fmt.Sprintf("监听器'%s'不存在", name),
		)
	}

	// 停止监听器
	if err := listener.Stop(); err != nil {
		c.logger.Error("停止监听器失败", "端点", name, "错误", err.Error())
		return err
	}

	// 从映射中移除
	delete(c.listeners, name)

	// 清理统计信息
	c.stats.mutex.Lock()
	delete(c.stats.ListenerStats, name)
	c.stats.mutex.Unlock()

	c.logger.Info("动态移除UDP监听器成功", "端点", name)
	return nil
}

// UpdateConfig 动态更新配置
func (c *udpListenerCluster) UpdateConfig(config *types.UDPConfig) error {
	if atomic.LoadInt32(&c.running) != 1 {
		return types.NewSystemError(
			types.ErrorCodeServiceUnavailable,
			"集群未运行，无法更新配置",
		)
	}

	// 验证新配置
	if err := config.Validate(); err != nil {
		return types.NewConfigError(
			types.ErrorCodeConfigValidationFailed,
			fmt.Sprintf("新配置验证失败: %v", err),
		)
	}

	c.logger.Info("开始动态更新UDP配置")

	// 获取新的启用端点
	newEndpoints := make(map[string]types.UDPEndpoint)
	for _, endpoint := range config.GetEnabledEndpoints() {
		newEndpoints[endpoint.Name] = endpoint
	}

	c.mutex.Lock()
	defer c.mutex.Unlock()

	// 移除不再需要的监听器
	for name, listener := range c.listeners {
		if _, needed := newEndpoints[name]; !needed {
			c.logger.Info("移除不再需要的监听器", "端点", name)
			if err := listener.Stop(); err != nil {
				c.logger.Error("停止监听器失败", "端点", name, "错误", err.Error())
			}
			delete(c.listeners, name)
		}
	}

	// 添加新的监听器
	for name, endpoint := range newEndpoints {
		if _, exists := c.listeners[name]; !exists {
			c.logger.Info("添加新的监听器", "端点", name)
			listener := NewUDPListener(endpoint, c.logger, c.bufferSize)
			if err := listener.Start(c.ctx); err != nil {
				c.logger.Error("启动新监听器失败", "端点", name, "错误", err.Error())
				continue
			}
			c.listeners[name] = listener
		}
	}

	c.logger.Info("UDP配置更新完成",
		"总监听器数", len(c.listeners),
		"新配置端点数", len(newEndpoints))

	return nil
}

// packetMerger 数据包合并器goroutine
func (c *udpListenerCluster) packetMerger() {
	defer c.wg.Done()

	// 创建一个用于选择的通道列表
	var cases []reflect.SelectCase
	var listenerNames []string

	for {
		select {
		case <-c.ctx.Done():
			return
		default:
		}

		// 重建选择案例列表
		cases = cases[:0]
		listenerNames = listenerNames[:0]

		// 添加上下文取消案例
		cases = append(cases, reflect.SelectCase{
			Dir:  reflect.SelectRecv,
			Chan: reflect.ValueOf(c.ctx.Done()),
		})
		listenerNames = append(listenerNames, "context")

		// 添加所有监听器的通道
		c.mutex.RLock()
		for name, listener := range c.listeners {
			if listener != nil {
				cases = append(cases, reflect.SelectCase{
					Dir:  reflect.SelectRecv,
					Chan: reflect.ValueOf(listener.GetPacketChannel()),
				})
				listenerNames = append(listenerNames, name)
			}
		}
		c.mutex.RUnlock()

		if len(cases) <= 1 {
			// 没有监听器，等待一会儿再检查
			time.Sleep(100 * time.Millisecond)
			continue
		}

		// 执行选择操作
		chosen, value, ok := reflect.Select(cases)
		if chosen == 0 {
			// 上下文被取消
			return
		}

		if !ok {
			// 通道关闭了，移除这个监听器
			listenerName := listenerNames[chosen]
			c.logger.Debug("监听器通道已关闭", "端点", listenerName)
			continue
		}

		// 获取数据包
		if packet, ok := value.Interface().(*PDXPPacket); ok && packet != nil {
			// 转发到合并通道
			select {
			case c.mergedChan <- packet:
			default:
				c.logger.Warn("合并通道已满，丢弃数据包", "源", packet.Source)
			}
		}
	}
}

// statsUpdater 统计信息更新器goroutine
func (c *udpListenerCluster) statsUpdater() {
	defer c.wg.Done()

	ticker := time.NewTicker(1 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-c.ctx.Done():
			return
		case <-ticker.C:
			c.updateClusterStats()
		}
	}
}

// updateClusterStats 更新集群统计信息
func (c *udpListenerCluster) updateClusterStats() {
	c.stats.mutex.Lock()
	defer c.stats.mutex.Unlock()

	c.stats.TotalListeners = 0
	c.stats.ActiveListeners = 0
	c.stats.HealthyListeners = 0
	c.stats.TotalPackets = 0
	c.stats.ValidPackets = 0
	c.stats.InvalidPackets = 0
	c.stats.TotalBytes = 0
	c.stats.TotalErrors = 0

	var totalPacketRate, totalByteRate float64

	c.mutex.RLock()
	for name, listener := range c.listeners {
		if listener == nil {
			continue
		}

		c.stats.TotalListeners++
		listenerStats := listener.GetStats()
		c.stats.ListenerStats[name] = listenerStats

		if listenerStats.IsConnected {
			c.stats.ActiveListeners++
		}

		if listener.IsHealthy() {
			c.stats.HealthyListeners++
		}

		c.stats.TotalPackets += listenerStats.TotalPackets
		c.stats.ValidPackets += listenerStats.ValidPackets
		c.stats.InvalidPackets += listenerStats.InvalidPackets
		c.stats.TotalBytes += listenerStats.TotalBytes
		c.stats.TotalErrors += listenerStats.ErrorCount

		totalPacketRate += listenerStats.PacketRate
		totalByteRate += listenerStats.ByteRate
	}
	c.mutex.RUnlock()

	// 计算平均速率
	if c.stats.TotalListeners > 0 {
		c.stats.AvgPacketRate = totalPacketRate / float64(c.stats.TotalListeners)
		c.stats.AvgByteRate = totalByteRate / float64(c.stats.TotalListeners)
	}

	// 更新集群健康状态
	c.updateClusterHealth()
	c.stats.LastUpdate = time.Now()
}

// updateClusterHealth 更新集群健康状态
func (c *udpListenerCluster) updateClusterHealth() {
	if c.stats.TotalListeners == 0 {
		c.stats.ClusterHealth = HealthStatusDown
		return
	}

	healthyRatio := float64(c.stats.HealthyListeners) / float64(c.stats.TotalListeners)

	switch {
	case healthyRatio >= 0.8:
		c.stats.ClusterHealth = HealthStatusHealthy
	case healthyRatio >= 0.5:
		c.stats.ClusterHealth = HealthStatusDegraded
	case c.stats.ActiveListeners > 0:
		c.stats.ClusterHealth = HealthStatusUnhealthy
	default:
		c.stats.ClusterHealth = HealthStatusDown
	}
}
