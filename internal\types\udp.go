// Package types 定义 Aquila 系统的核心数据类型
package types

import (
	"fmt"
	"net"
	"sync"
	"time"
)

// UDPEndpoint UDP 端点配置
// 对应 config.yaml 中的 udp 配置项
type UDPEndpoint struct {
	Name   string `yaml:"name" json:"name"`     // 唯一名称标识
	Addr   string `yaml:"addr" json:"addr"`     // 组播地址
	Port   int    `yaml:"port" json:"port"`     // 监听端口
	Desc   string `yaml:"desc" json:"desc"`     // 描述信息
	Enable bool   `yaml:"enable" json:"enable"` // 是否启用
}

// UDPConfig UDP 配置结构
// 包含所有 UDP 组播地址的配置信息
type UDPConfig struct {
	UDP []UDPEndpoint `yaml:"udp" json:"udp"` // UDP 端点列表
}

// Validate 验证UDP配置
func (c *UDPConfig) Validate() error {
	if len(c.UDP) == 0 {
		return fmt.Errorf("UDP配置不能为空")
	}

	names := make(map[string]bool)
	addrs := make(map[string]bool)

	for i, endpoint := range c.UDP {
		// 验证必填字段
		if endpoint.Name == "" {
			return fmt.Errorf("第%d个UDP端点的名称不能为空", i+1)
		}
		if endpoint.Addr == "" {
			return fmt.Errorf("UDP端点'%s'的地址不能为空", endpoint.Name)
		}
		if endpoint.Port <= 0 || endpoint.Port > 65535 {
			return fmt.Errorf("UDP端点'%s'的端口号必须在1-65535范围内", endpoint.Name)
		}

		// 验证名称唯一性
		if names[endpoint.Name] {
			return fmt.Errorf("UDP端点名称'%s'重复", endpoint.Name)
		}
		names[endpoint.Name] = true

		// 验证地址格式
		if net.ParseIP(endpoint.Addr) == nil {
			return fmt.Errorf("UDP端点'%s'的地址格式无效: %s", endpoint.Name, endpoint.Addr)
		}

		// 验证地址端口组合唯一性
		addrPort := fmt.Sprintf("%s:%d", endpoint.Addr, endpoint.Port)
		if addrs[addrPort] {
			return fmt.Errorf("UDP端点地址端口组合'%s'重复", addrPort)
		}
		addrs[addrPort] = true
	}

	return nil
}

// GetEnabledEndpoints 获取所有启用的UDP端点
func (c *UDPConfig) GetEnabledEndpoints() []UDPEndpoint {
	var enabled []UDPEndpoint
	for _, endpoint := range c.UDP {
		if endpoint.Enable {
			enabled = append(enabled, endpoint)
		}
	}
	return enabled
}

// GetEndpointByName 根据名称查找UDP端点
func (c *UDPConfig) GetEndpointByName(name string) *UDPEndpoint {
	for _, endpoint := range c.UDP {
		if endpoint.Name == name {
			return &endpoint
		}
	}
	return nil
}

// UDPConnection UDP连接信息
// 用于运行时管理UDP连接状态
type UDPConnection struct {
	Endpoint   UDPEndpoint  `json:"endpoint"`    // 端点配置
	Conn       *net.UDPConn `json:"-"`           // UDP连接对象（不序列化）
	LastPacket time.Time    `json:"last_packet"` // 最后接收包的时间
	PacketRate float64      `json:"packet_rate"` // 数据包接收速率（包/秒）
	ByteRate   float64      `json:"byte_rate"`   // 字节接收速率（字节/秒）
	TotalBytes int64        `json:"total_bytes"` // 总接收字节数
	TotalPkts  int64        `json:"total_pkts"`  // 总接收包数
	IsActive   bool         `json:"is_active"`   // 连接是否活跃
	ErrorCount int64        `json:"error_count"` // 错误计数
	LastError  string       `json:"last_error"`  // 最后错误信息

	// 并发安全保护
	mutex      sync.RWMutex `json:"-"` // 读写锁保护
	lastUpdate time.Time    `json:"-"` // 最后更新时间（用于速率计算）
}

// UpdateStats 更新连接统计信息
func (conn *UDPConnection) UpdateStats(bytesReceived int) {
	conn.mutex.Lock()
	defer conn.mutex.Unlock()

	now := time.Now()
	conn.TotalBytes += int64(bytesReceived)
	conn.TotalPkts++
	conn.LastPacket = now
	conn.IsActive = true

	// 计算速率（修复时间差计算逻辑）
	if !conn.lastUpdate.IsZero() {
		timeDiff := now.Sub(conn.lastUpdate).Seconds()
		if timeDiff > 0 {
			conn.PacketRate = 1.0 / timeDiff
			conn.ByteRate = float64(bytesReceived) / timeDiff
		}
	}
	conn.lastUpdate = now
}

// RecordError 记录错误信息
func (conn *UDPConnection) RecordError(err error) {
	conn.mutex.Lock()
	defer conn.mutex.Unlock()

	conn.ErrorCount++
	conn.LastError = err.Error()
	conn.IsActive = false
}

// UDPManager UDP管理器接口
// 定义UDP连接管理的核心方法
type UDPManager interface {
	// Start 启动UDP监听器
	Start(config *UDPConfig) error

	// Stop 停止UDP监听器
	Stop() error

	// GetConnections 获取所有连接状态
	GetConnections() map[string]*UDPConnection

	// GetConnection 根据名称获取连接状态
	GetConnection(name string) *UDPConnection

	// IsHealthy 检查UDP管理器健康状态
	IsHealthy() bool

	// GetStats 获取统计信息
	GetStats() UDPStats
}

// UDPStats UDP统计信息
type UDPStats struct {
	TotalEndpoints  int     `json:"total_endpoints"`  // 总端点数
	ActiveEndpoints int     `json:"active_endpoints"` // 活跃端点数
	TotalPackets    int64   `json:"total_packets"`    // 总接收包数
	TotalBytes      int64   `json:"total_bytes"`      // 总接收字节数
	AvgPacketRate   float64 `json:"avg_packet_rate"`  // 平均包速率
	AvgByteRate     float64 `json:"avg_byte_rate"`    // 平均字节速率
	ErrorCount      int64   `json:"error_count"`      // 总错误数
	Uptime          string  `json:"uptime"`           // 运行时长
}
