# Aquila PDXP Backend: PDXP数据监听与协议解析服务

## 项目概述

### 1.1. 背景与目标

* **背景**：企业内部使用基于UDP组播的PDXP协议进行数据交换，需要实现一个后端服务，监听给定的UDP组播地址和端口，收取并按要求解析PDXP数据，为前端服务提供数据实时推送与历史查询服务。
* **目标**：实现一个高性能的UDP组播监听与PDXP协议解析服务，通过Redis Timeseries缓存解析后的数据，维护程序状态，通过HTTP SSE向前端推送状态更新，并通过REST API为前端服务提供数据查询和状态查询服务。

### 1.2. 核心功能 (Features)

* **功能模块一：UDP数据监听**
  * 读取并解析`./config/config.yaml`中的UDP组播配置，获取组播名称、地址、端口、描述、是否需要监听等参数
  * 并发监听配置中的所有UDP组播地址，使用多goroutine实现高性能数据接收
  * 实现数据包完整性校验和PDXP载荷提取
  * 支持健康检查、自动重连等容错机制

* **功能模块二：PDXP协议解析**
  * 读取并解析`./config/config.yaml`中的PDXP结构配置，获取帧类型（包括类型名称、结构名称、组播名称、描述、是否需要解析、对应BID列表等）
  * 读取并解析`./config/pdxp/PDXP.yaml`中的协议基本配置，获取协议头定义（所有帧结构共享）
  * 根据帧类型所用的帧结构名称从`./config/pdxp/`中读取对应的帧结构定义文件（YAML格式），其中某些特殊配置项的定义在PDXP.yaml中通过scheme关键字说明
  * 实现配置驱动的协议解析引擎，通过PDXP载荷中的十六进制BID字段匹配帧类型，并将PDXP数据转换为结构化数据

* **功能模块三：数据存储与状态管理**
  * 使用Redis Timeseries对解析后的数据进行缓存，每个字段一个时间序列
  * 实现数据排序机制，确保存入Redis前所有序列按时间戳排序
  * 维护程序内部状态，根据解析得到的结构化数据实时更新状态信息
  * 支持批量写入优化和数据持久化策略，批量存储频率可配置（建议100-1000ms间隔）
  * 存储时以帧结构定义中class为key的字段值和参数名组合为存储键（按顺序以`.`分隔符连接）

* **功能模块四：HTTP SSE状态推送**
  * 支持跨域的HTTP SSE连接和多客户端并发连接管理
  * 前端服务发起连接时通过表单指定需要监控的状态字段，实现状态级数据过滤
  * 以可配置的频率向前端推送状态更新（建议1-10Hz），不同SSE连接可订阅相同或不同的状态
  * 基于状态变化触发推送，提高推送效率
  * 支持每个SSE连接独立配置推送频率和状态过滤条件

* **功能模块五：REST API服务**
  * 提供`GET /data`接口，支持分页查询、时间范围过滤等参数，供前端服务查询历史数据
  * 提供`GET /state`接口，返回程序维护的当前状态信息
  * 提供`DELETE /data`接口，将Redis中缓存的数据删除，支持批量删除操作
  * 提供`GET /metrics`接口，返回Prometheus格式的服务运行指标
  * 提供`GET /health`接口，返回服务的整体健康状态

* **功能模块六：协议解析线程池**
  * 实现基于线程池的并发协议解析，支持乱序处理提高解析性能
  * 使用worker pool模式，可配置工作线程数量
  * 解析完成后对数据按时间戳进行排序，确保存储顺序的正确性
  * 解析过程中同步更新程序状态，触发SSE状态推送
  * 集成背压控制机制防止内存溢出和任务堆积

* **功能模块七：服务运行状态监控**
  * 集成Prometheus指标收集，监控连接数、解析速度、错误率、资源使用等关键维度
  * 实现结构化日志输出（基于slog），支持多级别日志和实时监控
  * 提供健康检查端点和完整的指标查询接口，支持实时监控和告警能力
  * 监控各模块性能指标，为系统优化和故障排查提供数据支持

---

## 2. 技术架构

### 2.1. 技术选型 (Tech Stack)

| 技术类别 | 技术选型 | 版本要求 | 说明 |
|---------|---------|---------|------|
| 编程语言 | Go | >= 1.25.0 | 高并发网络编程 |
| HTTP框架 | Echo | >= 4.x | 轻量级Web框架 |
| 数据库 | Redis | >= 8.x | 时间序列数据缓存 |
| 配置解析 | gopkg.in/yaml.v3 | latest | YAML配置文件解析 |
| 日志框架 | slog | latest | 结构化日志 |
| 监控指标 | Prometheus | latest | 系统监控 |
| 并发控制 | sync.Pool + Worker Pool | latest | 线程池和对象池 |

### 2.2. 架构设计

```plaintext
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│     前端服务     │    │   Aquila后端    │    │ Redis Timeseries│
│                 │    │                 │    │                 │
│  ┌───────────┐  │    │  ┌───────────┐  │    │  ┌───────────┐  │
│  │ SSE Client│◄─┼────┼─►│ SSE Server│◄─┼────┼──│时间序列缓存 │  │
│  └───────────┘  │    │  └───────────┘  │    │  └───────────┘  │
│  ┌───────────┐  │    │  ┌───────────┐  │    │                 │
│  │REST Client│◄─┼────┼─►│REST Server│◄─┼────┼─►               │
│  └───────────┘  │    │  └───────────┘  │    │                 │
└─────────────────┘    │  ┌───────────┐  │    └─────────────────┘
                       │  │State Mgmt │  │
┌─────────────────┐    │  └─────┬─────┘  │
│    UDP组播源     │    │  ┌─────▼─────┐  │
│                 │    │  │Thread Pool│  │
│  230.25.1.1     │◄───┼──┤ Parser    │  │
│  :2511          │    │  └─────┬─────┘  │
│                 │    │  ┌─────▼─────┐  │
└─────────────────┘    │  │UDP Listener│ │
                       │  │ Goroutines│  │
                       │  └───────────┘  │
                       └─────────────────┘
```

**核心组件说明：**

1. **UDP监听器 (UDP Listener)**
   * 多goroutine并发监听多个组播地址，每个组播地址一个goroutine
   * 实现健康检查和自动重连机制
   * 数据包完整性校验

2. **PDXP解析线程池 (Thread Pool Parser)**
   * 基于worker pool的并发协议解析引擎
   * 支持嵌套数据结构和循环数据块
   * 高性能二进制数据解析，支持乱序并发处理
   * 解析完成后按时间戳排序，确保数据存储顺序正确

3. **数据存储与状态管理 (Data Storage & State Management)**
   * Redis Timeseries缓存：直接存储解析后的时间序列数据
   * 数据排序：存储前按时间戳排序确保数据顺序
   * 状态管理：维护程序内部状态，根据解析数据实时更新
   * 背压控制：防止内存溢出和存储堆积

4. **HTTP服务器 (HTTP Server)**
   * SSE推送：支持多客户端并发连接，推送状态更新
   * REST API：历史数据查询、状态查询和系统管理
   * 跨域支持
   * 高并发请求处理

### 2.3. 技术要点

* **并发设计**
  * UDP监听使用多goroutine并发处理
  * 协议解析使用worker pool线程池，支持乱序并发处理
  * 解析完成后按时间戳排序确保存储顺序
  * channel实现组件间异步通信
  * sync.Pool优化内存分配
  * context包实现优雅关闭

* **内存管理**
  * 对象池复用减少GC压力
  * 流式处理避免大对象缓存
  * 线程池任务队列大小限制和溢出保护
  * 状态数据结构优化，支持高频更新

* **错误处理**
  * 分层错误处理机制
  * 自动重试和熔断保护
  * 详细的错误日志和监控指标

---

## 3. 性能要求

### 3.1. 吞吐量指标

* **UDP数据接收**
  * 支持单个组播地址 >= 10,000 packets/sec
  * 支持同时监听 >= 10个组播地址
  * 单包最大处理能力 >= 1500 bytes
  * 总体数据吞吐量 >= 150 MB/sec

* **PDXP协议解析**
  * 线程池协议解析速度 >= 80,000 frames/sec
  * 复杂嵌套结构解析 >= 15,000 frames/sec
  * 内存使用效率：解析过程中内存占用 < 1GB
  * 线程池工作线程数：可配置，建议CPU核心数的2-4倍

* **HTTP SSE状态推送**
  * 支持并发SSE连接数 >= 100
  * 状态推送频率：可配置（建议1-10Hz）
  * 状态推送延迟 < 100ms
  * 支持不同连接订阅不同状态组合
  * 每个SSE连接可独立配置推送频率和状态过滤

* **Redis批量存储**
  * 批量存储频率：可配置（建议100-1000ms间隔）
  * 存储延迟：< 50ms per batch
  * 支持存储失败重试和错误恢复

* **状态管理**
  * 状态更新频率 >= 10,000 updates/sec
  * 状态查询响应时间 < 50ms
  * 支持并发状态查询 >= 200 req/sec

* **REST API查询**
  * 历史数据查询响应时间 < 500ms
  * 支持并发查询请求 >= 100 req/sec
  * 大数据量查询（1万条记录）< 2s

### 3.2. 延迟指标

* **端到端延迟**
  * UDP接收到数据缓存：< 50ms (P99)

* **系统响应延迟**
  * REST API响应时间：< 100ms (P95)
  * 健康检查响应：< 50ms
  * 指标查询响应：< 200ms

* **数据处理延迟**
  * PDXP解析延迟：< 0.5ms per frame（线程池并发）
  * 数据排序延迟：< 5ms per batch
  * Redis Timeseries写入延迟：< 10ms (P95)
  * 状态更新延迟：< 1ms

### 3.3. 数据完整性要求

* **数据包无丢失**
  * UDP数据包接收完整性：> 99.9%
  * 提供丢包统计和告警功能

* **数据存储有序性**
  * 通过时间戳排序确保Redis存储数据的时序正确性
  * 状态更新基于最新数据，确保状态一致性

---

## 4. 开发要求

### 4.1. 开发环境

* **基础环境**
  * Go版本：>= 1.25.0
  * 操作系统：Windows 10/11, Linux (Ubuntu 20.04+), macOS
  * 内存要求：>= 8GB RAM
  * 磁盘空间：>= 10GB 可用空间

* **开发工具**
  * IDE：推荐使用GoLand、VS Code + Go插件
  * 版本控制：Git >= 2.30
  * 包管理：Go Modules
  * 构建工具：Make 或 PowerShell脚本

* **外部依赖**
  * Redis Timeseries：>= 8.0（用于时间序列数据缓存）
  * 网络环境：支持UDP组播的网络配置
  * 测试环境：支持组播数据源模拟

### 4.2. 开发规范

* **代码规范**
  * 严格遵循Go官方代码规范（gofmt, golint, go vet）
  * 使用有意义的变量和函数命名（英文，驼峰命名法）
  * 每个包必须有package注释说明其用途
  * 所有函数和结构体必须有详细的注释文档

* **项目结构规范**

```plaintext
  aquila/
  ├── cmd/                   # 主程序入口
  │   └── aquila/
  │       └── main.go
  ├── internal/              # 内部包，不对外暴露
  ├── pkg/                   # 可复用的公共包
  ├── api/                   # API文档和定义
  ├── config/                # 配置文件
  ├── docs/                  # 项目文档
  ├── scripts/               # 构建和部署脚本
  └── tests/                 # 测试文件
```

* **错误处理规范**
  * 使用Go标准的error接口
  * 关键路径必须有详细的错误日志
  * 实现自定义错误类型，包含错误码和详细信息
  * 使用context包实现超时和取消机制
  * 错误信息使用中文

* **并发安全规范**
  * 共享数据必须使用mutex或channel保护
  * 避免goroutine泄漏，确保所有goroutine能正常退出
  * 使用sync.WaitGroup等待goroutine完成
  * 合理使用缓冲channel避免阻塞
  * 线程池实现必须支持优雅关闭和任务取消
  * 状态管理必须保证并发安全，使用适当的同步机制

* **测试规范**
  * 单元测试覆盖率 >= 80%
  * 关键模块必须有集成测试
  * 使用testify等测试框架提高测试质量
  * 性能测试：关键路径必须有benchmark测试

* **注释与文档规范**
  * 所有函数必须有完整的GoDoc注释
  * 复杂算法和业务逻辑必须有详细的行内注释
  * 每个包的doc.go文件说明包的用途和使用方法
  * API接口必须有完整的OpenAPI 3.0文档
  * 配置文件必须有详细的字段说明和示例
  * 重要的设计决策和技术选型必须有文档记录
  * 注释和文档使用中文

### 4.3. 配置要求

* **配置文件结构**
  * 主配置文件：`config/config.yaml`
  * PDXP协议配置：`config/pdxp/PDXP.yaml`
  * PDXP帧结构定义：`config/pdxp/*.yaml`（按需创建）

* **配置验证要求**
  * 启动时必须验证所有配置文件的有效性
  * 配置错误时提供清晰的错误信息和修复建议
  * 支持配置文件语法检查工具
