package udp

import (
	"context"
	"log/slog"
	"net"
	"os"
	"sync"
	"testing"
	"time"

	"aquila/internal/types"
)

// 测试用的模拟组播地址
const (
	testMulticastAddr = "224.0.0.1"
	testPort          = 12345
	testBufferSize    = 1024
)

// setupTestLogger 创建测试用的日志器
func setupTestLogger() *slog.Logger {
	return slog.New(slog.NewTextHandler(os.Stdout, &slog.HandlerOptions{
		Level: slog.LevelDebug,
	}))
}

// createTestEndpoint 创建测试用的UDP端点
func createTestEndpoint(name, addr string, port int) types.UDPEndpoint {
	return types.UDPEndpoint{
		Name:   name,
		Addr:   addr,
		Port:   port,
		Desc:   "测试端点",
		Enable: true,
	}
}

// sendTestPacket 发送测试数据包
func sendTestPacket(t *testing.T, addr string, port int, data []byte) {
	conn, err := net.Dial("udp", net.JoinHostPort(addr, string(rune(port))))
	if err != nil {
		t.Fatalf("创建测试发送连接失败: %v", err)
	}
	defer conn.Close()

	_, err = conn.Write(data)
	if err != nil {
		t.Errorf("发送测试数据失败: %v", err)
	}
}

// createValidPDXPPacket 创建有效的PDXP数据包
func createValidPDXPPacket(bid, packetNum uint32, payload []byte) []byte {
	packet := make([]byte, 8+len(payload))

	// BID (大端序)
	packet[0] = byte(bid >> 24)
	packet[1] = byte(bid >> 16)
	packet[2] = byte(bid >> 8)
	packet[3] = byte(bid)

	// Packet Number (大端序)
	packet[4] = byte(packetNum >> 24)
	packet[5] = byte(packetNum >> 16)
	packet[6] = byte(packetNum >> 8)
	packet[7] = byte(packetNum)

	// Payload
	copy(packet[8:], payload)

	return packet
}

func TestNewUDPListener(t *testing.T) {
	logger := setupTestLogger()
	endpoint := createTestEndpoint("test", testMulticastAddr, testPort)

	listener := NewUDPListener(endpoint, logger, testBufferSize)

	if listener == nil {
		t.Fatal("创建UDP监听器失败")
	}

	if listener.GetEndpoint().Name != "test" {
		t.Errorf("端点名称不匹配，期望: test, 实际: %s", listener.GetEndpoint().Name)
	}
}

func TestUDPListener_BasicOperations(t *testing.T) {
	logger := setupTestLogger()
	endpoint := createTestEndpoint("test", testMulticastAddr, testPort)
	listener := NewUDPListener(endpoint, logger, testBufferSize)

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	// 测试启动
	err := listener.Start(ctx)
	if err != nil {
		t.Fatalf("启动UDP监听器失败: %v", err)
	}

	// 等待监听器完全启动
	time.Sleep(100 * time.Millisecond)

	// 测试健康检查
	if !listener.IsHealthy() {
		t.Error("监听器应该是健康的")
	}

	// 测试获取统计信息
	stats := listener.GetStats()
	if stats == nil {
		t.Error("获取统计信息失败")
	}
	if stats.Name != "test" {
		t.Errorf("统计信息中的名称不匹配，期望: test, 实际: %s", stats.Name)
	}

	// 测试停止
	err = listener.Stop()
	if err != nil {
		t.Errorf("停止UDP监听器失败: %v", err)
	}
}

func TestUDPListener_PacketProcessing(t *testing.T) {
	logger := setupTestLogger()
	endpoint := createTestEndpoint("test", "127.0.0.1", testPort+1) // 使用本地地址进行测试
	listener := NewUDPListener(endpoint, logger, testBufferSize)

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	// 启动监听器
	err := listener.Start(ctx)
	if err != nil {
		t.Fatalf("启动UDP监听器失败: %v", err)
	}
	defer listener.Stop()

	// 等待监听器启动
	time.Sleep(100 * time.Millisecond)

	// 发送测试数据包
	testPayload := []byte("测试载荷数据")
	testPacket := createValidPDXPPacket(0x12345678, 1001, testPayload)

	// 由于组播测试复杂，这里主要测试数据包处理逻辑
	go func() {
		time.Sleep(200 * time.Millisecond)
		// 在真实环境中，这里会发送UDP数据包
		// sendTestPacket(t, endpoint.Addr, endpoint.Port, testPacket)
		_ = testPacket // 避免未使用变量警告
	}()

	// 获取数据包通道
	packetChan := listener.GetPacketChannel()
	if packetChan == nil {
		t.Fatal("获取数据包通道失败")
	}

	// 由于测试环境限制，我们主要验证通道不为空
	select {
	case <-time.After(1 * time.Second):
		// 超时是正常的，因为我们没有真正发送数据包
		t.Log("数据包接收测试超时（预期行为）")
	case packet := <-packetChan:
		// 如果收到数据包，验证其结构
		if packet == nil {
			t.Error("接收到空数据包")
		}
		t.Logf("接收到PDXP数据包: BID=0x%08X, PacketNum=%d", packet.BID, packet.PacketNumber)
	}
}

func TestPDXPPacketExtraction(t *testing.T) {
	logger := setupTestLogger()
	endpoint := createTestEndpoint("test", testMulticastAddr, testPort+2)
	listener := NewUDPListener(endpoint, logger, testBufferSize).(*udpListener)

	// 测试有效数据包
	testPayload := []byte("测试载荷")
	validPacket := createValidPDXPPacket(0x87654321, 2002, testPayload)

	udpPacket := &UDPPacket{
		Source:     "test",
		Data:       validPacket,
		Size:       len(validPacket),
		Timestamp:  time.Now(),
		RemoteAddr: &net.UDPAddr{IP: net.ParseIP("127.0.0.1"), Port: 12345},
	}

	pdxpPacket := listener.extractPDXPPacket(udpPacket)

	if !pdxpPacket.IsValid {
		t.Error("有效PDXP数据包被标记为无效")
	}

	if pdxpPacket.BID != 0x87654321 {
		t.Errorf("BID解析错误，期望: 0x87654321, 实际: 0x%08X", pdxpPacket.BID)
	}

	if pdxpPacket.PacketNumber != 2002 {
		t.Errorf("包序号解析错误，期望: 2002, 实际: %d", pdxpPacket.PacketNumber)
	}

	if len(pdxpPacket.Payload) != len(testPayload) {
		t.Errorf("载荷长度错误，期望: %d, 实际: %d", len(testPayload), len(pdxpPacket.Payload))
	}

	// 测试无效数据包（太短）
	invalidPacket := &UDPPacket{
		Source:    "test",
		Data:      []byte{1, 2, 3}, // 少于8字节
		Size:      3,
		Timestamp: time.Now(),
	}

	pdxpPacket = listener.extractPDXPPacket(invalidPacket)

	if pdxpPacket.IsValid {
		t.Error("无效PDXP数据包被标记为有效")
	}

	// 测试零BID数据包
	zeroBIDPacket := createValidPDXPPacket(0, 1000, testPayload)
	udpPacket.Data = zeroBIDPacket
	udpPacket.Size = len(zeroBIDPacket)

	pdxpPacket = listener.extractPDXPPacket(udpPacket)

	if pdxpPacket.IsValid {
		t.Error("零BID数据包应该被标记为无效")
	}
}

func TestNewUDPListenerCluster(t *testing.T) {
	logger := setupTestLogger()

	cluster := NewUDPListenerCluster(logger, testBufferSize, 1000)

	if cluster == nil {
		t.Fatal("创建UDP监听器集群失败")
	}

	stats := cluster.GetClusterStats()
	if stats == nil {
		t.Error("获取集群统计信息失败")
	}

	if stats.ClusterHealth != HealthStatusDown {
		t.Errorf("新集群健康状态应该是Down，实际: %s", stats.ClusterHealth)
	}
}

func TestUDPCluster_BasicOperations(t *testing.T) {
	logger := setupTestLogger()
	cluster := NewUDPListenerCluster(logger, testBufferSize, 1000)

	// 创建测试配置
	config := &types.UDPConfig{
		UDP: []types.UDPEndpoint{
			createTestEndpoint("test1", testMulticastAddr, testPort+10),
			createTestEndpoint("test2", testMulticastAddr, testPort+11),
		},
	}

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	// 测试启动
	err := cluster.Start(ctx, config)
	if err != nil {
		t.Fatalf("启动UDP集群失败: %v", err)
	}
	defer cluster.Stop()

	// 等待集群完全启动
	time.Sleep(200 * time.Millisecond)

	// 测试获取监听器
	listeners := cluster.GetListeners()
	if len(listeners) != 2 {
		t.Errorf("监听器数量不正确，期望: 2, 实际: %d", len(listeners))
	}

	listener1 := cluster.GetListener("test1")
	if listener1 == nil {
		t.Error("获取监听器test1失败")
	}

	listener2 := cluster.GetListener("test2")
	if listener2 == nil {
		t.Error("获取监听器test2失败")
	}

	// 测试合并通道
	mergedChan := cluster.GetAllPacketChannels()
	if mergedChan == nil {
		t.Error("获取合并通道失败")
	}

	// 测试集群统计
	stats := cluster.GetClusterStats()
	if stats.TotalListeners != 2 {
		t.Errorf("集群总监听器数不正确，期望: 2, 实际: %d", stats.TotalListeners)
	}
}

func TestUDPCluster_DynamicManagement(t *testing.T) {
	logger := setupTestLogger()
	cluster := NewUDPListenerCluster(logger, testBufferSize, 1000)

	// 空配置启动
	config := &types.UDPConfig{
		UDP: []types.UDPEndpoint{
			createTestEndpoint("initial", testMulticastAddr, testPort+20),
		},
	}

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	err := cluster.Start(ctx, config)
	if err != nil {
		t.Fatalf("启动UDP集群失败: %v", err)
	}
	defer cluster.Stop()

	time.Sleep(100 * time.Millisecond)

	// 测试动态添加监听器
	newEndpoint := createTestEndpoint("dynamic", testMulticastAddr, testPort+21)
	err = cluster.AddListener(newEndpoint)
	if err != nil {
		t.Errorf("动态添加监听器失败: %v", err)
	}

	time.Sleep(100 * time.Millisecond)

	listeners := cluster.GetListeners()
	if len(listeners) != 2 {
		t.Errorf("添加监听器后数量不正确，期望: 2, 实际: %d", len(listeners))
	}

	// 测试动态移除监听器
	err = cluster.RemoveListener("dynamic")
	if err != nil {
		t.Errorf("动态移除监听器失败: %v", err)
	}

	time.Sleep(100 * time.Millisecond)

	listeners = cluster.GetListeners()
	if len(listeners) != 1 {
		t.Errorf("移除监听器后数量不正确，期望: 1, 实际: %d", len(listeners))
	}

	// 测试配置更新
	newConfig := &types.UDPConfig{
		UDP: []types.UDPEndpoint{
			createTestEndpoint("updated1", testMulticastAddr, testPort+22),
			createTestEndpoint("updated2", testMulticastAddr, testPort+23),
		},
	}

	err = cluster.UpdateConfig(newConfig)
	if err != nil {
		t.Errorf("更新配置失败: %v", err)
	}

	time.Sleep(200 * time.Millisecond)

	listeners = cluster.GetListeners()
	if len(listeners) != 2 {
		t.Errorf("更新配置后监听器数量不正确，期望: 2, 实际: %d", len(listeners))
	}

	// 验证新监听器
	if cluster.GetListener("updated1") == nil {
		t.Error("更新后的监听器updated1不存在")
	}

	if cluster.GetListener("updated2") == nil {
		t.Error("更新后的监听器updated2不存在")
	}

	// 验证旧监听器已被移除
	if cluster.GetListener("initial") != nil {
		t.Error("旧监听器initial应该已被移除")
	}
}

func TestHealthStatus(t *testing.T) {
	stats := &UDPListenerStats{
		TotalPackets:   1000,
		ValidPackets:   900,
		InvalidPackets: 100,
		ErrorCount:     50,
		IsConnected:    true,
		LastPacketTime: time.Now(),
	}

	stats.updateHealthStatus()

	// 连接正常，错误率较低，应该是健康的
	if stats.Health != HealthStatusHealthy {
		t.Errorf("健康状态应该是Healthy，实际: %s", stats.Health)
	}

	// 测试高错误率情况
	stats.ErrorCount = 150 // 15%错误率
	stats.updateHealthStatus()

	if stats.Health != HealthStatusDegraded {
		t.Errorf("高错误率时健康状态应该是Degraded，实际: %s", stats.Health)
	}

	// 测试断开连接情况
	stats.IsConnected = false
	stats.updateHealthStatus()

	if stats.Health != HealthStatusDown {
		t.Errorf("断开连接时健康状态应该是Down，实际: %s", stats.Health)
	}

	// 测试长时间无数据情况
	stats.IsConnected = true
	stats.ErrorCount = 50                                    // 恢复正常错误率
	stats.LastPacketTime = time.Now().Add(-40 * time.Second) // 40秒前
	stats.updateHealthStatus()

	if stats.Health != HealthStatusUnhealthy {
		t.Errorf("长时间无数据时健康状态应该是Unhealthy，实际: %s", stats.Health)
	}
}

// 并发安全性测试
func TestConcurrentSafety(t *testing.T) {
	logger := setupTestLogger()
	endpoint := createTestEndpoint("concurrent", testMulticastAddr, testPort+30)
	listener := NewUDPListener(endpoint, logger, testBufferSize)

	ctx, cancel := context.WithTimeout(context.Background(), 3*time.Second)
	defer cancel()

	err := listener.Start(ctx)
	if err != nil {
		t.Fatalf("启动监听器失败: %v", err)
	}
	defer listener.Stop()

	// 并发读取统计信息
	var wg sync.WaitGroup
	for i := 0; i < 10; i++ {
		wg.Add(1)
		go func() {
			defer wg.Done()
			for j := 0; j < 100; j++ {
				stats := listener.GetStats()
				if stats == nil {
					t.Error("并发获取统计信息失败")
				}
				time.Sleep(time.Millisecond)
			}
		}()
	}

	wg.Wait()
}

// 基准测试
func BenchmarkPDXPPacketExtraction(b *testing.B) {
	logger := setupTestLogger()
	endpoint := createTestEndpoint("bench", testMulticastAddr, testPort+40)
	listener := NewUDPListener(endpoint, logger, testBufferSize).(*udpListener)

	testPayload := []byte("基准测试载荷数据")
	testPacket := createValidPDXPPacket(0x12345678, 1001, testPayload)

	udpPacket := &UDPPacket{
		Source:     "bench",
		Data:       testPacket,
		Size:       len(testPacket),
		Timestamp:  time.Now(),
		RemoteAddr: &net.UDPAddr{IP: net.ParseIP("127.0.0.1"), Port: 12345},
	}

	b.ResetTimer()
	b.ReportAllocs()

	for i := 0; i < b.N; i++ {
		pdxpPacket := listener.extractPDXPPacket(udpPacket)
		if !pdxpPacket.IsValid {
			b.Error("PDXP包提取失败")
		}
	}
}
