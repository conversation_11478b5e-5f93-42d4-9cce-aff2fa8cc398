# Aquila PDXP Backend 实现计划

## 任务概述

基于完整的需求分析和技术设计，本实现计划将Aquila PDXP Backend项目分解为原子化的开发任务。每个任务都遵循15-30分钟的完成时间，单一职责原则，并明确指定了涉及的文件和复用的现有代码。

## Steering文档合规性

**遵循structure.md约定**：

- 严格按照Go项目标准目录结构组织代码
- 使用internal/包组织内部业务逻辑
- 遵循Go命名规范和包管理最佳实践
- 实现>=80%单元测试覆盖率

**符合tech.md模式**：

- 使用指定的技术栈（Go + Echo + Redis + YAML + slog + Prometheus）
- 实现高并发设计和内存优化策略
- 错误处理使用中文信息，并发安全设计
- 集成对象池和worker pool优化模式

## 原子任务要求

每个任务严格满足以下标准以确保代理友好执行：

**文件范围**：每个任务涉及1-3个相关文件最大
**时间限制**：15-30分钟可完成的开发量
**单一目标**：一个可测试的功能点
**具体文件**：明确指定要创建/修改的文件路径
**代理友好**：清晰的输入输出，最小化上下文切换

## 任务格式指南

- 使用复选框格式：`- [ ] 任务编号. 任务描述`
- **指定文件**：始终包含确切的文件路径
- **包含实现细节**：作为要点列出
- 引用需求：`_Requirements: X.Y, Z.A_`
- 引用现有代码：`_Leverage: path/to/file, existing/component_`
- 仅关注编码任务（无部署、用户测试等）

## 实现任务

### 阶段 1: 基础设施和配置管理

- [x] 1. 更新go.mod添加核心依赖包
  - 文件: go.mod
  - 添加Echo v4.x, Redis client, YAML v3, slog, Prometheus等依赖
  - 设置Go版本为1.25.0，配置module依赖版本
  - 目的: 建立项目依赖基础，支持后续组件开发
  - _Leverage: 现有go.mod基础配置_
  - _Requirements: 所有技术栈需求_

- [x] 2. 创建项目基础目录结构
  - 文件: internal/目录结构创建
  - 创建internal/config, internal/udp, internal/parser, internal/storage等包目录
  - 添加各包的doc.go文件说明包用途
  - 目的: 建立标准Go项目结构，遵循structure.md规范
  - _Leverage: 现有cmd/aquila目录结构_
  - _Requirements: 项目结构标准_

- [x] 3. 定义核心数据类型和配置结构
  - 文件: internal/types/config.go
  - 定义UDPConfig, PDXPFrameType, PDXPSchema等核心数据结构
  - 添加YAML标签和JSON序列化支持
  - 目的: 建立类型安全的配置数据结构
  - _Leverage: 现有config.yaml结构定义_
  - _Requirements: 1.1, 2.1_

- [x] 4. 实现配置文件加载器
  - 文件: internal/config/loader.go
  - 实现ConfigManager接口，支持config.yaml和pdxp/*.yaml文件加载
  - 添加配置文件验证和错误处理机制
  - 目的: 提供配置驱动的系统初始化能力
  - _Leverage: 现有完整的config目录结构_
  - _Requirements: 1.1, 2.1_

- [x] 5. 添加配置热重载功能
  - 文件: internal/config/watcher.go
  - 实现文件系统监控，支持配置文件变更检测
  - 添加配置变更事件通知机制
  - 目的: 支持动态配置更新，减少系统重启
  - _Leverage: Go标准库fsnotify或类似包_
  - _Requirements: 2.3_

- [x] 6. 创建配置管理器单元测试
  - 文件: internal/config/loader_test.go
  - 测试配置文件解析、验证、错误处理功能
  - 使用testdata/目录存放测试配置文件
  - 目的: 确保配置加载的可靠性和错误处理正确性
  - _Leverage: 现有config文件作为测试用例_
  - _Requirements: 测试覆盖率>=80%_

### 阶段 2: UDP网络监听组件

- [x] 7. 实现UDP监听器基础类
  - 文件: internal/udp/listener.go
  - 实现UDPListener接口，支持组播地址监听
  - 添加连接健康检查和自动重连机制
  - 目的: 提供可靠的UDP组播数据接收能力
  - _Leverage: Go net包的UDP组播功能_
  - _Requirements: 1.1, 1.3_

- [x] 8. 实现UDP监听器集群管理
  - 文件: internal/udp/cluster.go
  - 实现UDPListenerCluster接口，管理多个监听器实例
  - 支持并发启动和优雅关闭所有监听器
  - 目的: 支持22个组播地址的并发监听
  - _Leverage: internal/udp/listener.go, Go goroutine并发_
  - _Requirements: 1.1, 1.5_

- [-] 9. 添加UDP数据包验证器
  - 文件: internal/udp/validator.go
  - 实现数据包完整性校验和PDXP载荷提取
  - 添加统计信息收集和错误处理
  - 目的: 确保数据包质量和提取PDXP有效载荷
  - _Leverage: 二进制数据处理和校验算法_
  - _Requirements: 1.2_

- [x] 10. 创建UDP组件单元测试
  - 文件: internal/udp/listener_test.go
  - 测试UDP监听器的连接、重连、数据接收功能
  - 使用模拟UDP数据源进行测试
  - 目的: 验证网络组件的稳定性和错误恢复能力
  - _Leverage: net包的测试工具，模拟网络环境_
  - _Requirements: 1.1-1.5_

### 阶段 3: PDXP协议解析引擎

- [ ] 11. 实现PDXP协议解析器核心
  - 文件: internal/parser/engine.go
  - 实现PDXPParser接口，支持BID字段匹配和帧类型识别
  - 添加二进制数据解析和结构化数据转换
  - 目的: 提供基于配置的动态协议解析能力
  - _Leverage: config/pdxp/协议定义文件_
  - _Requirements: 2.1, 2.2_

- [ ] 12. 实现PDXP协议模式加载器
  - 文件: internal/parser/schema.go
  - 加载和解析PDXP.yaml和各种帧结构定义文件
  - 支持嵌套数据结构和循环数据块解析规则
  - 目的: 动态加载88种帧类型的解析配置
  - _Leverage: config/pdxp/目录下的所有YAML文件_
  - _Requirements: 2.1, 2.2_

- [ ] 13. 添加PDXP数据类型转换器
  - 文件: internal/parser/converter.go
  - 实现各种数据类型转换（uint8-32, 位字段, 数学函数）
  - 支持hex, identity, date, time, mul, linear等转换函数
  - 目的: 将原始二进制数据转换为具有业务意义的数值
  - _Leverage: PDXP.yaml中的func定义_
  - _Requirements: 2.1, 2.2_

- [ ] 14. 实现解析错误处理和统计
  - 文件: internal/parser/errors.go
  - 定义解析错误类型和中文错误信息
  - 添加解析统计信息收集和性能监控
  - 目的: 提供详细的错误信息和性能指标
  - _Leverage: Go error接口和中文错误消息_
  - _Requirements: 2.5, 7.2_

- [ ] 15. 创建PDXP解析器单元测试
  - 文件: internal/parser/engine_test.go
  - 测试各种帧类型的解析正确性
  - 使用真实的PDXP协议配置和测试数据
  - 目的: 验证协议解析的准确性和性能
  - _Leverage: config/pdxp/中的协议定义作为测试用例_
  - _Requirements: 2.1-2.5_

### 阶段 4: 线程池和并发处理

- [ ] 16. 实现解析任务队列
  - 文件: internal/worker/queue.go
  - 实现带缓冲的任务队列，支持背压控制
  - 添加队列状态监控和溢出保护
  - 目的: 提供高效的任务调度和流量控制
  - _Leverage: Go channel和context包_
  - _Requirements: 6.4_

- [ ] 17. 实现解析线程池
  - 文件: internal/worker/pool.go
  - 实现ParserWorkerPool接口，支持并发解析任务
  - 可配置worker数量，支持乱序处理
  - 目的: 实现高性能并发协议解析
  - _Leverage: sync.Pool优化和goroutine管理_
  - _Requirements: 6.1, 6.2_

- [ ] 18. 实现数据排序器
  - 文件: internal/worker/sorter.go
  - 实现按时间戳排序的数据批处理器
  - 使用堆排序确保数据时序正确性
  - 目的: 确保存储数据的时间顺序正确
  - _Leverage: container/heap包_
  - _Requirements: 6.3_

- [ ] 19. 添加worker池优雅关闭
  - 文件: internal/worker/shutdown.go
  - 实现优雅关闭机制，完成正在处理的任务
  - 支持超时强制关闭和资源清理
  - 目的: 确保系统安全关闭，不丢失数据
  - _Leverage: context和sync.WaitGroup_
  - _Requirements: 6.5_

- [ ] 20. 创建worker池单元测试
  - 文件: internal/worker/pool_test.go
  - 测试并发处理、排序正确性、优雅关闭
  - 使用模拟解析任务进行性能测试
  - 目的: 验证线程池的并发安全和性能
  - _Leverage: 并发测试工具和benchmark测试_
  - _Requirements: 6.1-6.5_

### 阶段 5: Redis存储和状态管理

- [ ] 21. 实现Redis时间序列客户端
  - 文件: internal/storage/redis.go
  - 实现TimeseriesStorage接口，支持批量存储和查询
  - 添加连接池管理和错误重试机制
  - 目的: 提供高性能的时间序列数据存储
  - _Leverage: Redis client库和连接池_
  - _Requirements: 3.1, 3.4_

- [ ] 22. 实现存储键生成器
  - 文件: internal/storage/keys.go
  - 基于解析结果的class字段生成存储键
  - 支持层级结构和参数名组合键格式
  - 目的: 提供一致的数据键命名和查询支持
  - _Leverage: 解析结果的ClassKeys字段_
  - _Requirements: 3.2_

- [ ] 23. 实现状态管理器
  - 文件: internal/state/manager.go
  - 实现StateManager接口，支持并发安全的状态操作
  - 添加状态变化通知和订阅机制
  - 目的: 维护系统运行状态，支持实时监控
  - _Leverage: sync.RWMutex和event notification_
  - _Requirements: 3.3, 4.2_

- [ ] 24. 添加存储重试和降级机制
  - 文件: internal/storage/retry.go
  - 实现指数退避重试算法
  - 支持降级到内存缓存的故障恢复
  - 目的: 提高存储操作的可靠性
  - _Leverage: 重试算法和内存缓存_
  - _Requirements: 3.5_

- [ ] 25. 创建存储组件单元测试
  - 文件: internal/storage/redis_test.go
  - 测试Redis操作、重试机制、状态管理
  - 使用Redis测试容器或内存Redis
  - 目的: 验证存储操作的正确性和可靠性
  - _Leverage: Redis测试工具和模拟数据_
  - _Requirements: 3.1-3.5_

### 阶段 6: HTTP服务和API

- [ ] 26. 实现Echo HTTP服务器基础
  - 文件: internal/http/server.go
  - 初始化Echo框架，配置中间件和路由
  - 添加CORS支持和基础安全中间件
  - 目的: 建立HTTP服务基础框架
  - _Leverage: Echo框架和中间件生态_
  - _Requirements: 4.1, 5.1_

- [ ] 27. 实现REST API数据查询接口
  - 文件: internal/http/api.go
  - 实现GET /data接口，支持分页和时间范围查询
  - 添加请求参数验证和响应格式化
  - 目的: 提供历史数据查询功能
  - _Leverage: internal/storage Redis查询接口_
  - _Requirements: 5.1_

- [ ] 28. 实现状态查询和管理接口
  - 文件: internal/http/state.go
  - 实现GET /state接口返回系统状态
  - 实现DELETE /data接口支持数据删除
  - 目的: 提供状态监控和数据管理功能
  - _Leverage: internal/state状态管理器_
  - _Requirements: 5.2, 5.3_

- [ ] 29. 实现健康检查和监控接口
  - 文件: internal/http/health.go
  - 实现GET /health和GET /metrics接口
  - 集成Prometheus指标收集和展示
  - 目的: 提供系统健康状态和性能监控
  - _Leverage: Prometheus库和各组件健康状态_
  - _Requirements: 5.4, 5.5, 7.1_

- [ ] 30. 创建HTTP服务单元测试
  - 文件: internal/http/api_test.go
  - 测试各API接口的功能和错误处理
  - 使用HTTP测试工具验证响应格式
  - 目的: 确保API接口的正确性和稳定性
  - _Leverage: Echo测试工具和HTTP测试框架_
  - _Requirements: 5.1-5.6_

### 阶段 7: SSE实时推送服务

- [ ] 31. 实现SSE连接管理器
  - 文件: internal/sse/manager.go
  - 实现客户端连接注册、注销和管理
  - 支持连接过滤器和推送频率配置
  - 目的: 管理多客户端SSE连接
  - _Leverage: Echo SSE支持和连接管理_
  - _Requirements: 4.1, 4.2_

- [ ] 32. 实现SSE推送服务
  - 文件: internal/sse/service.go
  - 实现SSEService接口，支持状态变化推送
  - 添加推送频率控制和错误处理
  - 目的: 提供实时状态推送功能
  - _Leverage: 状态管理器的变化通知_
  - _Requirements: 4.3, 4.5_

- [ ] 33. 添加SSE性能优化
  - 文件: internal/sse/optimizer.go
  - 实现推送缓冲和批量发送优化
  - 添加连接健康检查和自动清理
  - 目的: 提高SSE推送性能和连接稳定性
  - _Leverage: 缓冲机制和连接监控_
  - _Requirements: 4.4, 4.5_

- [ ] 34. 创建SSE服务单元测试
  - 文件: internal/sse/service_test.go
  - 测试SSE连接管理、推送功能、性能优化
  - 使用模拟客户端验证推送正确性
  - 目的: 验证SSE服务的功能和性能
  - _Leverage: SSE测试工具和模拟连接_
  - _Requirements: 4.1-4.5_

### 阶段 8: 系统监控和日志

- [ ] 35. 实现结构化日志系统
  - 文件: internal/logging/logger.go
  - 基于slog实现中文日志输出
  - 支持多级别日志和上下文信息
  - 目的: 提供统一的结构化日志服务
  - _Leverage: Go slog标准库_
  - _Requirements: 7.2_

- [ ] 36. 实现Prometheus指标收集器
  - 文件: internal/metrics/collector.go
  - 收集系统关键指标（连接数、解析速度、错误率等）
  - 集成到各个组件中进行实时监控
  - 目的: 提供全面的性能和运行指标
  - _Leverage: Prometheus Go客户端库_
  - _Requirements: 7.1, 7.4_

- [ ] 37. 实现健康检查器
  - 文件: internal/health/checker.go
  - 检查各组件运行状态和连接健康
  - 提供详细的健康状态报告
  - 目的: 支持系统监控和故障诊断
  - _Leverage: 各组件的健康状态接口_
  - _Requirements: 7.4_

- [ ] 38. 创建监控组件单元测试
  - 文件: internal/metrics/collector_test.go
  - 测试指标收集、日志输出、健康检查功能
  - 验证监控数据的准确性
  - 目的: 确保监控系统的可靠性
  - _Leverage: 测试工具和模拟数据_
  - _Requirements: 7.1-7.5_

### 阶段 9: 主程序和系统集成

- [ ] 39. 实现应用程序启动器
  - 文件: internal/app/app.go
  - 集成所有组件，实现系统初始化和启动流程
  - 支持优雅关闭和资源清理
  - 目的: 提供完整的应用程序生命周期管理
  - _Leverage: 所有已实现的内部组件_
  - _Requirements: 所有功能需求_

- [ ] 40. 更新主程序入口
  - 文件: cmd/aquila/main.go
  - 替换hello world代码为实际的应用程序启动
  - 添加命令行参数解析和配置文件路径
  - 目的: 提供可执行的应用程序入口
  - _Leverage: internal/app应用启动器_
  - _Requirements: 应用程序入口点_

- [ ] 41. 添加系统信号处理
  - 文件: internal/app/signals.go
  - 实现优雅关闭信号处理（SIGINT, SIGTERM）
  - 确保所有组件能够安全关闭
  - 目的: 支持生产环境的安全部署和关闭
  - _Leverage: Go signal包和context取消_
  - _Requirements: 7.5_

- [ ] 42. 创建应用程序集成测试
  - 文件: tests/integration_test.go
  - 测试完整的数据流：UDP接收→解析→存储→API查询
  - 使用真实配置和模拟数据源
  - 目的: 验证系统端到端功能的正确性
  - _Leverage: 所有组件和测试工具_
  - _Requirements: 所有功能需求_

### 阶段 10: 文档和部署

- [ ] 43. 创建API接口文档
  - 文件: api/openapi.yaml
  - 基于实现的REST API生成OpenAPI 3.0规范
  - 包含所有接口的请求/响应格式和示例
  - 目的: 提供完整的API使用文档
  - _Leverage: 已实现的HTTP API接口_
  - _Requirements: API文档需求_

- [ ] 44. 更新项目README文档
  - 文件: README.md
  - 添加项目介绍、安装指南、配置说明、API使用示例
  - 包含性能指标和部署建议
  - 目的: 提供项目的完整使用指南
  - _Leverage: 实现的功能和配置文件_
  - _Requirements: 项目文档需求_

- [ ] 45. 创建部署配置和脚本
  - 文件: scripts/build.sh, scripts/deploy.sh
  - 添加跨平台构建脚本和部署配置
  - 包含Docker配置和systemd服务文件
  - 目的: 支持生产环境部署
  - _Leverage: Go构建工具和部署最佳实践_
  - _Requirements: 部署和运维需求_

## 验证和测试任务

所有实现任务完成后，执行以下验证：

- [ ] 46. 运行完整测试套件
  - 执行所有单元测试、集成测试，确保覆盖率>=80%
  - 运行性能基准测试，验证性能指标
  - _Requirements: 测试覆盖率和性能需求_

- [ ] 47. 验证功能完整性
  - 测试22个UDP组播地址监听
  - 验证88种PDXP帧类型解析
  - 确认所有API接口正常工作
  - _Requirements: 所有功能性需求_

- [ ] 48. 性能压力测试
  - 验证>=10,000 packets/sec UDP接收能力
  - 测试>=80,000 frames/sec解析性能
  - 确认<50ms端到端延迟目标
  - _Requirements: 所有性能需求_

## 审查提示词

你是一名资深软件架构师和高级Go语言开发专家，需要对当前项目进行严格的代码审查。目前项目已完成UDP模块的开发，具体完成的工作见 @d:\Files\Projects\Go\src\aquila/tasks.md 中的任务7-10，注意任务9目前不需要实现。请检查`./internal/udp/`目录下的代码文件，分析代码质量，确保实现方式符合项目设计要求、满足架构一致性、功能完整、实现方式无冲突无重复无冗余且符合Go语言最佳实践、测试覆盖全面、测试完整通过、代码符合预期。
