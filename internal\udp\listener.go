// Package udp 实现UDP监听器基础类
// 支持高性能UDP组播数据监听，数据包验证和PDXP载荷提取
package udp

import (
	"context"
	"fmt"
	"log/slog"
	"net"
	"sync"
	"sync/atomic"
	"time"

	"aquila/internal/types"
)

const (
	// DefaultBufferSize UDP接收缓冲区默认大小
	DefaultBufferSize = 65536

	// DefaultReadTimeout UDP读取超时时间
	DefaultReadTimeout = 5 * time.Second

	// DefaultReconnectDelay 重连延迟时间
	DefaultReconnectDelay = 2 * time.Second

	// MaxReconnectDelay 最大重连延迟
	MaxReconnectDelay = 30 * time.Second

	// DefaultPacketChannelSize 数据包通道缓冲区大小
	DefaultPacketChannelSize = 1000

	// MinPDXPPacketSize PDXP数据包最小大小
	MinPDXPPacketSize = 8

	// PDXPHeaderSize PDXP包头大小
	PDXPHeaderSize = 8
)

// UDPPacket UDP数据包结构
type UDPPacket struct {
	Source     string       // 数据源名称
	Data       []byte       // 原始数据
	Size       int          // 数据大小
	Timestamp  time.Time    // 接收时间
	RemoteAddr *net.UDPAddr // 远程地址
}

// PDXPPacket PDXP数据包结构
type PDXPPacket struct {
	UDPPacket
	BID          uint32 // 数据标志 (前4字节)
	PacketNumber uint32 // 包序号 (后4字节)
	Payload      []byte // PDXP载荷数据
	IsValid      bool   // 数据是否有效
}

// UDPListener UDP监听器接口
type UDPListener interface {
	// Start 启动UDP监听器
	Start(ctx context.Context) error

	// Stop 停止UDP监听器
	Stop() error

	// GetPacketChannel 获取数据包通道
	GetPacketChannel() <-chan *PDXPPacket

	// GetStats 获取统计信息
	GetStats() *UDPListenerStats

	// IsHealthy 检查健康状态
	IsHealthy() bool

	// GetEndpoint 获取端点配置
	GetEndpoint() types.UDPEndpoint
}

// UDPListenerStats UDP监听器统计信息
type UDPListenerStats struct {
	Name           string       `json:"name"`             // 监听器名称
	StartTime      time.Time    `json:"start_time"`       // 启动时间
	LastPacketTime time.Time    `json:"last_packet_time"` // 最后数据包时间
	TotalPackets   int64        `json:"total_packets"`    // 总接收包数
	ValidPackets   int64        `json:"valid_packets"`    // 有效包数
	InvalidPackets int64        `json:"invalid_packets"`  // 无效包数
	TotalBytes     int64        `json:"total_bytes"`      // 总接收字节数
	PacketRate     float64      `json:"packet_rate"`      // 包接收速率(包/秒)
	ByteRate       float64      `json:"byte_rate"`        // 字节接收速率(字节/秒)
	ErrorCount     int64        `json:"error_count"`      // 错误计数
	ReconnectCount int64        `json:"reconnect_count"`  // 重连次数
	LastError      string       `json:"last_error"`       // 最后错误
	IsConnected    bool         `json:"is_connected"`     // 连接状态
	Health         HealthStatus `json:"health"`           // 健康状态
	mutex          sync.RWMutex `json:"-"`                // 读写锁
}

// HealthStatus 健康状态枚举
type HealthStatus string

const (
	HealthStatusHealthy   HealthStatus = "healthy"   // 健康
	HealthStatusUnhealthy HealthStatus = "unhealthy" // 不健康
	HealthStatusDegraded  HealthStatus = "degraded"  // 降级
	HealthStatusDown      HealthStatus = "down"      // 宕机
)

// updatedStats 更新统计信息（线程安全）
func (s *UDPListenerStats) updateStats(bytesReceived int, isValid bool, hasError bool, errorMsg string) {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	now := time.Now()
	s.TotalPackets++
	s.TotalBytes += int64(bytesReceived)
	s.LastPacketTime = now

	if isValid {
		s.ValidPackets++
	} else {
		s.InvalidPackets++
	}

	if hasError {
		s.ErrorCount++
		s.LastError = errorMsg
	}

	// 计算速率（简单移动平均）
	if !s.StartTime.IsZero() {
		duration := now.Sub(s.StartTime).Seconds()
		if duration > 0 {
			s.PacketRate = float64(s.TotalPackets) / duration
			s.ByteRate = float64(s.TotalBytes) / duration
		}
	}

	// 更新健康状态
	s.updateHealthStatus()
}

// updateHealthStatus 更新健康状态
func (s *UDPListenerStats) updateHealthStatus() {
	now := time.Now()

	// 如果未连接，优先标记为宕机
	if !s.IsConnected {
		s.Health = HealthStatusDown
		return
	}

	// 如果超过30秒没有收到数据包，标记为不健康
	if !s.LastPacketTime.IsZero() && now.Sub(s.LastPacketTime) > 30*time.Second {
		s.Health = HealthStatusUnhealthy
		return
	}

	// 如果错误率超过10%，标记为降级
	if s.TotalPackets > 0 {
		errorRate := float64(s.ErrorCount) / float64(s.TotalPackets)
		if errorRate > 0.1 {
			s.Health = HealthStatusDegraded
			return
		}
	}

	// 其他情况标记为健康
	s.Health = HealthStatusHealthy
}

// udpListener UDP监听器实现
type udpListener struct {
	endpoint       types.UDPEndpoint
	conn           *net.UDPConn
	packetChan     chan *PDXPPacket
	stats          *UDPListenerStats
	logger         *slog.Logger
	ctx            context.Context
	cancel         context.CancelFunc
	wg             sync.WaitGroup
	bufferSize     int
	readTimeout    time.Duration
	reconnectDelay time.Duration
	running        int32 // 原子操作标记
}

// NewUDPListener 创建新的UDP监听器
func NewUDPListener(endpoint types.UDPEndpoint, logger *slog.Logger, bufferSize int) UDPListener {
	if bufferSize <= 0 {
		bufferSize = DefaultBufferSize
	}

	return &udpListener{
		endpoint:       endpoint,
		packetChan:     make(chan *PDXPPacket, DefaultPacketChannelSize),
		bufferSize:     bufferSize,
		readTimeout:    DefaultReadTimeout,
		reconnectDelay: DefaultReconnectDelay,
		logger:         logger.With("component", "udp-listener", "endpoint", endpoint.Name),
		stats: &UDPListenerStats{
			Name:      endpoint.Name,
			StartTime: time.Now(),
			Health:    HealthStatusDown,
		},
	}
}

// Start 启动UDP监听器
func (l *udpListener) Start(ctx context.Context) error {
	if !atomic.CompareAndSwapInt32(&l.running, 0, 1) {
		return types.NewNetworkError(
			types.ErrorCodeUDPListenFailed,
			fmt.Sprintf("UDP监听器'%s'已在运行", l.endpoint.Name),
		)
	}

	l.ctx, l.cancel = context.WithCancel(ctx)

	l.logger.Info("启动UDP监听器",
		"地址", l.endpoint.Addr,
		"端口", l.endpoint.Port)

	// 启动主监听循环
	l.wg.Add(1)
	go l.listenLoop()

	return nil
}

// Stop 停止UDP监听器
func (l *udpListener) Stop() error {
	if !atomic.CompareAndSwapInt32(&l.running, 1, 0) {
		return nil // 已经停止
	}

	l.logger.Info("停止UDP监听器", "端点", l.endpoint.Name)

	// 取消上下文
	if l.cancel != nil {
		l.cancel()
	}

	// 关闭连接
	if l.conn != nil {
		l.conn.Close()
	}

	// 等待goroutine结束
	l.wg.Wait()

	// 关闭通道
	close(l.packetChan)

	l.logger.Info("UDP监听器已停止", "端点", l.endpoint.Name)
	return nil
}

// GetPacketChannel 获取数据包通道
func (l *udpListener) GetPacketChannel() <-chan *PDXPPacket {
	return l.packetChan
}

// GetStats 获取统计信息
func (l *udpListener) GetStats() *UDPListenerStats {
	l.stats.mutex.RLock()
	defer l.stats.mutex.RUnlock()

	// 返回副本以避免并发访问问题
	statsCopy := *l.stats
	return &statsCopy
}

// IsHealthy 检查健康状态
func (l *udpListener) IsHealthy() bool {
	stats := l.GetStats()
	return stats.Health == HealthStatusHealthy || stats.Health == HealthStatusDegraded
}

// GetEndpoint 获取端点配置
func (l *udpListener) GetEndpoint() types.UDPEndpoint {
	return l.endpoint
}

// listenLoop 主监听循环
func (l *udpListener) listenLoop() {
	defer l.wg.Done()

	reconnectDelay := l.reconnectDelay

	for atomic.LoadInt32(&l.running) == 1 {
		select {
		case <-l.ctx.Done():
			return
		default:
		}

		// 尝试建立连接
		if err := l.connect(); err != nil {
			l.logger.Error("UDP连接失败", "错误", err.Error())
			l.stats.updateStats(0, false, true, err.Error())

			// 指数退避重连
			time.Sleep(reconnectDelay)
			reconnectDelay = min(reconnectDelay*2, MaxReconnectDelay)
			continue
		}

		// 重置重连延迟
		reconnectDelay = l.reconnectDelay

		// 开始接收数据
		l.receiveLoop()

		// 连接断开，更新统计信息
		l.stats.mutex.Lock()
		l.stats.IsConnected = false
		l.stats.ReconnectCount++
		l.stats.mutex.Unlock()
	}
}

// connect 建立UDP连接
func (l *udpListener) connect() error {
	addr := fmt.Sprintf("%s:%d", l.endpoint.Addr, l.endpoint.Port)
	udpAddr, err := net.ResolveUDPAddr("udp", addr)
	if err != nil {
		return types.NewNetworkError(
			types.ErrorCodeInvalidAddress,
			fmt.Sprintf("解析UDP地址失败 %s", addr),
		).WithCause(err)
	}

	// 创建UDP连接
	conn, err := net.ListenMulticastUDP("udp", nil, udpAddr)
	if err != nil {
		return types.NewNetworkError(
			types.ErrorCodeUDPBindFailed,
			fmt.Sprintf("绑定UDP组播地址失败 %s", addr),
		).WithCause(err)
	}

	// 设置读取缓冲区大小
	if err := conn.SetReadBuffer(l.bufferSize); err != nil {
		l.logger.Warn("设置UDP读取缓冲区大小失败", "错误", err.Error())
	}

	l.conn = conn
	l.stats.mutex.Lock()
	l.stats.IsConnected = true
	l.stats.mutex.Unlock()

	l.logger.Info("UDP连接建立成功",
		"地址", addr,
		"缓冲区大小", l.bufferSize)

	return nil
}

// receiveLoop 数据接收循环
func (l *udpListener) receiveLoop() {
	buffer := make([]byte, l.bufferSize)

	for atomic.LoadInt32(&l.running) == 1 {
		select {
		case <-l.ctx.Done():
			return
		default:
		}

		// 设置读取超时
		l.conn.SetReadDeadline(time.Now().Add(l.readTimeout))

		// 接收数据
		n, remoteAddr, err := l.conn.ReadFromUDP(buffer)
		if err != nil {
			if netErr, ok := err.(net.Error); ok && netErr.Timeout() {
				// 超时是正常的，继续循环
				continue
			}

			l.logger.Error("UDP数据接收失败", "错误", err.Error())
			l.stats.updateStats(0, false, true, err.Error())
			return // 退出接收循环，触发重连
		}

		// 处理接收到的数据
		l.processPacket(buffer[:n], remoteAddr)
	}
}

// processPacket 处理收到的数据包
func (l *udpListener) processPacket(data []byte, remoteAddr *net.UDPAddr) {
	timestamp := time.Now()

	// 创建UDP数据包
	udpPacket := &UDPPacket{
		Source:     l.endpoint.Name,
		Data:       make([]byte, len(data)),
		Size:       len(data),
		Timestamp:  timestamp,
		RemoteAddr: remoteAddr,
	}
	copy(udpPacket.Data, data)

	// 验证和提取PDXP数据
	pdxpPacket := l.extractPDXPPacket(udpPacket)

	// 更新统计信息
	l.stats.updateStats(len(data), pdxpPacket.IsValid, false, "")

	// 发送到通道
	select {
	case l.packetChan <- pdxpPacket:
	default:
		// 通道满了，丢弃数据包并记录警告
		l.logger.Warn("数据包通道已满，丢弃数据包",
			"源", l.endpoint.Name,
			"大小", len(data))
		l.stats.updateStats(0, false, true, "数据包通道已满")
	}
}

// extractPDXPPacket 提取PDXP数据包
func (l *udpListener) extractPDXPPacket(udpPacket *UDPPacket) *PDXPPacket {
	pdxpPacket := &PDXPPacket{
		UDPPacket: *udpPacket,
		IsValid:   false,
	}

	// 验证数据包最小大小
	if len(udpPacket.Data) < MinPDXPPacketSize {
		l.logger.Debug("数据包大小不足",
			"源", udpPacket.Source,
			"大小", len(udpPacket.Data),
			"最小要求", MinPDXPPacketSize)
		return pdxpPacket
	}

	// 提取PDXP包头（前8字节）
	// BID (数据标志): 前4字节，大端序
	pdxpPacket.BID = uint32(udpPacket.Data[0])<<24 |
		uint32(udpPacket.Data[1])<<16 |
		uint32(udpPacket.Data[2])<<8 |
		uint32(udpPacket.Data[3])

	// PacketNumber (包序号): 后4字节，大端序
	pdxpPacket.PacketNumber = uint32(udpPacket.Data[4])<<24 |
		uint32(udpPacket.Data[5])<<16 |
		uint32(udpPacket.Data[6])<<8 |
		uint32(udpPacket.Data[7])

	// 提取PDXP载荷数据
	if len(udpPacket.Data) > PDXPHeaderSize {
		pdxpPacket.Payload = udpPacket.Data[PDXPHeaderSize:]
	}

	// 验证BID合法性（非零）
	if pdxpPacket.BID != 0 {
		pdxpPacket.IsValid = true
		l.logger.Debug("PDXP数据包提取成功",
			"源", udpPacket.Source,
			"BID", fmt.Sprintf("0x%08X", pdxpPacket.BID),
			"包序号", pdxpPacket.PacketNumber,
			"载荷大小", len(pdxpPacket.Payload))
	}

	return pdxpPacket
}
