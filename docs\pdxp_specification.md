# PDXP 协议规范文档

## 协议概述

PDXP（测控系统试验任务信息交换格式）是航天测控系统中用于数据交换的标准协议。本文档基于《测控系统试验任务信息交换格式约定》v3.0版本。

- **协议版本**：3.0
- **协议名称**：PDXP+
- **数据传输**：UDP组播
- **字节序**：大端序（网络字节序）

## 数据包结构

PDXP数据包由固定的32字节包头和可变长度的数据域组成：

```plaintext
+------------------+------------------+
|   PDXP Header    |    Data Payload  |
|    (32 bytes)    |   (variable)     |
+------------------+------------------+
```

## PDXP包头结构

PDXP包头为32字节固定长度，包含以下字段：

| 偏移 | 字段名 | 类型 | 长度 | 描述 | 转换函数 |
|------|--------|------|------|------|----------|
| 0    | Ver    | uint8  | 1    | 版本号 | hex |
| 1    | Mid    | uint16 | 2    | 任务标志 | hex |
| 3    | Sid    | uint32 | 4    | 信源地址 | hex |
| 7    | Did    | uint32 | 4    | 信宿地址 | hex |
| 11   | Bid    | uint32 | 4    | 数据标志（业务标识） | hex |
| 15   | Num    | uint32 | 4    | 包序号 | identity |
| 19   | Flag   | uint8  | 1    | 数据处理标志 | hex |
| 20   | Reserved | uint32 | 4  | 保留字段 | hex |
| 24   | Date   | uint16 | 2    | 发送日期 | date |
| 26   | Time   | uint32 | 4    | 发送时标 | time |
| 30   | L      | uint16 | 2    | 数据域长度 | identity |

### 关键字段说明

#### BID（业务标识）

- **用途**：用于识别数据包的业务类型和帧结构
- **格式**：32位无符号整数
- **示例**：0x00100101（光电设备测量帧）

#### 时间字段

- **Date**：距离2000年1月1日的天数
- **Time**：当天00:00:00开始的秒数×10
- **精度**：0.1秒

#### 数据域长度（L）

- **含义**：数据域的字节长度
- **范围**：0-65535字节
- **用途**：用于解析变长数据域

## 数据类型定义

### 基础数据类型

| 类型 | 长度 | 范围 | 描述 |
|------|------|------|------|
| uint8  | 1字节 | 0-255 | 8位无符号整数 |
| uint16 | 2字节 | 0-65535 | 16位无符号整数 |
| uint32 | 4字节 | 0-4294967295 | 32位无符号整数 |
| int8   | 1字节 | -128-127 | 8位有符号整数 |
| int16  | 2字节 | -32768-32767 | 16位有符号整数 |
| int32  | 4字节 | -2147483648-2147483647 | 32位有符号整数 |

### 数据块类型

| 类型 | 描述 | 特点 |
|------|------|------|
| static | 定长数据块 | 固定字段，长度确定 |
| dynamic | 变长数据块 | 固定字段+循环数据块 |
| repeated | 循环数据块 | 可重复的数据结构 |

### 数据字类型

| 类型 | 长度 | 描述 |
|------|------|------|
| bit1 | 1位 | 布尔值（0/1） |
| bit2 | 2位 | 4种取值（0-3） |
| bit3 | 3位 | 8种取值（0-7） |
| bit4 | 4位 | 16种取值（0-15） |

## 转换函数

### 数值转换

| 函数 | 公式 | 描述 | 示例 |
|------|------|------|------|
| identity | y = x | 原值不变 | 123 → 123 |
| mul | y = k × x | 乘法转换 | x=1000, k=0.001 → 1.0 |
| linear | y = a × x + b | 线性转换 | x=100, a=0.1, b=10 → 20.0 |

### 格式转换

| 函数 | 描述 | 示例 |
|------|------|------|
| hex | 十六进制字符串 | 255 → "0xFF" |
| date | 日期转换 | 9000 → "2024-08-22" |
| time | 时间转换 | 123456 → "03:25:45.6" |

## 帧结构定义

### 配置文件格式

帧结构定义使用YAML格式，支持嵌套和循环结构：

```yaml
# 帧结构示例
GX:
  desc: 光电设备测量帧
  class: block
  type: dynamic
  members:
  - name: T
    desc: 当前时刻
    class: field
    type: uint32
    func: time
  - name: Status
    desc: 设备状态
    class: field
    type: uint8
    func: hex
  - name: Targets
    desc: 多个测量目标
    class: block
    type: repeated
    count: ${N}  # 引用前置字段N的值
    members:
    - name: DA
      desc: 方位脱靶量
      class: field
      type: int32
      func: mul 0.1
```

### 字段属性

| 属性 | 必填 | 描述 | 示例 |
|------|------|------|------|
| name | 是 | 字段名称 | "T", "Status" |
| desc | 是 | 字段描述 | "当前时刻" |
| class | 是 | 字段类别 | block, field, key, word |
| type | 是 | 数据类型 | uint32, static, repeated |
| func | 否 | 转换函数 | time, hex, mul 0.1 |
| count | 否 | 循环次数 | 10, ${N} |
| len | 否 | 块长度 | 32 |

## BID映射表

常用的BID值及其对应的帧类型：

| BID | 帧类型 | 描述 | 数据源 |
|-----|--------|------|--------|
| 0x00100101 | GX | 一平面光学设备测量帧 | tracking_primary |
| 0x00100102 | GX | 一平面光学设备测量帧 | tracking_primary |
| 0x00200201 | GX | 二平面光学设备测量帧 | tracking_secondary |
| 0x00200202 | GX | 二平面光学设备测量帧 | tracking_secondary |

## 解析流程

### 1. 包头解析

1. 读取32字节包头数据
2. 按照包头结构解析各字段
3. 验证版本号和数据长度
4. 根据BID确定帧类型

### 2. 数据域解析

1. 根据BID查找对应的帧结构定义
2. 按照帧结构递归解析数据域
3. 处理循环数据块和变长字段
4. 应用转换函数得到最终值

### 3. 数据验证

1. 检查数据长度一致性
2. 验证字段值范围
3. 检查必填字段完整性
4. 验证循环计数有效性

## 错误处理

### 常见错误类型

| 错误类型 | 描述 | 处理方式 |
|----------|------|----------|
| 长度不匹配 | 实际数据长度与L字段不符 | 丢弃数据包 |
| BID未知 | 无对应的帧结构定义 | 记录日志，跳过解析 |
| 字段缺失 | 必填字段数据不足 | 使用默认值或丢弃 |
| 类型错误 | 数据类型转换失败 | 记录错误，使用默认值 |

### 容错机制

1. **部分解析**：即使部分字段解析失败，也保留成功解析的字段
2. **默认值**：为关键字段提供合理的默认值
3. **日志记录**：详细记录解析错误信息，便于调试
4. **统计监控**：统计解析成功率和错误类型

## 性能优化

### 解析优化

1. **预编译模式**：启动时预编译帧结构定义
2. **对象池**：复用解析对象，减少GC压力
3. **并发解析**：使用线程池并发处理多个数据包
4. **缓存机制**：缓存常用的帧结构和转换结果

### 内存优化

1. **流式解析**：避免缓存大量原始数据
2. **字段复用**：复用字段对象减少内存分配
3. **压缩存储**：对历史数据进行压缩存储
4. **分页查询**：大数据量查询使用分页机制

## 扩展性设计

### 版本兼容

1. **向后兼容**：新版本支持旧版本数据格式
2. **版本检测**：根据Ver字段选择解析策略
3. **渐进升级**：支持混合版本环境运行
4. **配置迁移**：提供配置文件升级工具

### 协议扩展

1. **新字段添加**：支持在不影响现有字段的情况下添加新字段
2. **新类型支持**：扩展数据类型和转换函数
3. **自定义函数**：支持用户定义的转换函数
4. **插件机制**：支持第三方解析插件

## 相关文档

- [配置文档](configuration.md)：系统配置说明
- [动态Header设计](dynamic_header_design.md)：Header动态解析机制
- [技术架构](.augment/rules/tech.md)：系统技术架构
- [任务列表](../tasks.md)：开发任务规划
