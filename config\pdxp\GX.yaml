# 此配置文件用于描述光电设备测量帧的数据域格式
version: 3.0

GX:
  desc: 光电设备测量帧
  class: block
  type: dynamic
  members:
  - name: T
    desc: 当前时刻
    class: field
    type: uint32
    func: time
  - name: Status
    desc: 设备状态
    class: field
    type: uint8
    func: hex
    members:
    - name: reserved
      desc: 保留位
      class: word
      type: bit3
      values:
    - name: television_type
      desc: 电视类型
      class: word
      type: bit1
      values:
        0b0: 卫星电视自动跟踪
        0b1: 导弹电视自动跟踪
    - name: tracking_status
      desc: 跟踪状态
      class: word
      type: bit4
      values:
        0b0000: 半自动未跟踪目标
        0b0001: 程序引导
        0b0010: 数字引导
        0b0011: 同步引导
        0b0100: 半自动跟踪
        0b0101: 电视自动跟踪
        0b0110: 中波红外自动跟踪
        0b0111: 长波红外自动跟踪
        0b1000: 短波红外自动跟踪
        0b1001: 多路复合跟踪
        0b1010: 激光自动跟踪
        0b1011: 雷达自动跟踪
  - name: A
    desc: 方位角
    class: field
    type: int32
    func: mul 1.676e-07
  - name: E
    desc: 俯仰角
    class: field
    type: int32
    func: mul 1.676e-07
  - name: N
    desc: 目标总数
    class: field
    type: uint8
    func: identity
  - name: Targets
    desc: 多个测量目标
    class: block
    type: repeated
    count: ${N}
    members:
    - name: DA
      desc: 方位脱靶量
      class: field
      type: int32
      func: mul 0.1
    - name: DE
      desc: 俯仰脱靶量
      class: field
      type: int32
      func: mul 0.1
    - name: V
      desc: 测速值
      class: field
      type: int32
      func: mul 0.001
    - name: R
      desc: 测距值
      class: field
      type: int32
      func: mul 0.01
    - name: L
      desc: 亮度
      class: field
      type: int16
      func: mul 0.1
