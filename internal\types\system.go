// Package types 定义系统状态和解析结果相关的核心数据类型
package types

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"sync"
	"time"
)

// ParsedFrame 解析后的帧数据结构
// 用于存储完整的解析结果，包含时间戳和结构化数据
type ParsedFrame struct {
	ID           string         `json:"id"`         // 唯一标识符
	Timestamp    time.Time      `json:"timestamp"`  // 解析时间戳
	Source       string         `json:"source"`     // 数据源名称
	FrameType    string         `json:"frame_type"` // 帧类型名称
	BID          uint32         `json:"bid"`        // 数据标志
	PacketNumber uint32         `json:"packet_num"` // 包序号
	Size         int            `json:"size"`       // 数据大小
	Fields       map[string]any `json:"fields"`     // 结构化字段数据
	Metadata     FrameMetadata  `json:"metadata"`   // 元数据信息
	Quality      DataQuality    `json:"quality"`    // 数据质量信息
}

// FrameMetadata 帧元数据
type FrameMetadata struct {
	ParseDuration time.Duration     `json:"parse_duration"` // 解析耗时
	ParseTime     time.Time         `json:"parse_time"`     // 解析时刻
	Version       string            `json:"version"`        // 协议版本
	Schema        string            `json:"schema"`         // 使用的模式名称
	Parser        string            `json:"parser"`         // 解析器标识
	Flags         map[string]string `json:"flags"`          // 额外标记
}

// DataQuality 数据质量信息
type DataQuality struct {
	IsValid      bool     `json:"is_valid"`     // 数据是否有效
	Completeness float64  `json:"completeness"` // 完整性(0-1)
	Accuracy     float64  `json:"accuracy"`     // 准确性(0-1)
	Freshness    float64  `json:"freshness"`    // 新鲜度(0-1)
	Consistency  bool     `json:"consistency"`  // 一致性
	Errors       []string `json:"errors"`       // 错误列表
	Warnings     []string `json:"warnings"`     // 警告列表
}

// GetRedisKeys 生成用于Redis时序存储的键名列表
func (f *ParsedFrame) GetRedisKeys() []string {
	var keys []string
	baseKey := f.GetBaseRedisKey()

	// 递归遍历所有字段生成键名
	f.generateKeysRecursive(baseKey, "", f.Fields, &keys)

	return keys
}

// GetBaseRedisKey 获取基础Redis键名
func (f *ParsedFrame) GetBaseRedisKey() string {
	return fmt.Sprintf("aquila:%s:%s:0x%08X", f.Source, f.FrameType, f.BID)
}

// generateKeysRecursive 递归生成字段键名
func (f *ParsedFrame) generateKeysRecursive(baseKey, path string, fields map[string]any, keys *[]string) {
	for fieldName, fieldValue := range fields {
		currentPath := fieldName
		if path != "" {
			currentPath = path + "." + fieldName
		}

		switch v := fieldValue.(type) {
		case map[string]any:
			// 嵌套对象，递归处理
			f.generateKeysRecursive(baseKey, currentPath, v, keys)
		case []any:
			// 数组，为每个元素生成键名
			for i, item := range v {
				arrayPath := fmt.Sprintf("%s[%d]", currentPath, i)
				if itemMap, ok := item.(map[string]any); ok {
					f.generateKeysRecursive(baseKey, arrayPath, itemMap, keys)
				} else {
					*keys = append(*keys, fmt.Sprintf("%s:%s", baseKey, arrayPath))
				}
			}
		default:
			// 基础类型，直接生成键名
			*keys = append(*keys, fmt.Sprintf("%s:%s", baseKey, currentPath))
		}
	}
}

// GetFieldValue 获取指定路径的字段值
func (f *ParsedFrame) GetFieldValue(path string) (any, bool) {
	return f.getValueByPath(f.Fields, path)
}

// getValueByPath 根据路径获取值，支持嵌套和数组访问
func (f *ParsedFrame) getValueByPath(data map[string]any, path string) (any, bool) {
	// 实现路径解析逻辑，支持"field.subfield"和"field[index]"格式
	// 这里简化实现，实际需要更完整的路径解析
	if value, exists := data[path]; exists {
		return value, true
	}
	return nil, false
}

// SystemState 系统状态信息
type SystemState struct {
	ServiceName    string                    `json:"service_name"`    // 服务名称
	Version        string                    `json:"version"`         // 版本号
	StartTime      time.Time                 `json:"start_time"`      // 启动时间
	Uptime         time.Duration             `json:"uptime"`          // 运行时长
	Status         ServiceStatus             `json:"status"`          // 服务状态
	UDPConnections map[string]*UDPConnection `json:"udp_connections"` // UDP连接状态
	ParseStats     ParseStatistics           `json:"parse_stats"`     // 解析统计
	ResourceUsage  ResourceUsage             `json:"resource_usage"`  // 资源使用情况
	Errors         []SystemError             `json:"errors"`          // 系统错误
	LastUpdate     time.Time                 `json:"last_update"`     // 最后更新时间
	mutex          sync.RWMutex              `json:"-"`               // 读写锁
}

// ServiceStatus 服务状态枚举
type ServiceStatus string

const (
	StatusStarting ServiceStatus = "starting" // 启动中
	StatusRunning  ServiceStatus = "running"  // 运行中
	StatusStopping ServiceStatus = "stopping" // 停止中
	StatusStopped  ServiceStatus = "stopped"  // 已停止
	StatusError    ServiceStatus = "error"    // 错误状态
)

// ParseStatistics 解析统计信息
type ParseStatistics struct {
	TotalFrames    int64                     `json:"total_frames"`     // 总帧数
	SuccessFrames  int64                     `json:"success_frames"`   // 成功解析帧数
	ErrorFrames    int64                     `json:"error_frames"`     // 解析失败帧数
	ParseRate      float64                   `json:"parse_rate"`       // 解析速率(帧/秒)
	SuccessRate    float64                   `json:"success_rate"`     // 成功率
	AvgParseTime   time.Duration             `json:"avg_parse_time"`   // 平均解析时间
	FrameTypeStats map[string]*FrameTypeStat `json:"frame_type_stats"` // 按帧类型统计
	LastUpdateTime time.Time                 `json:"last_update"`      // 最后更新时间
}

// FrameTypeStat 帧类型统计信息
type FrameTypeStat struct {
	Name          string        `json:"name"`           // 帧类型名称
	TotalCount    int64         `json:"total_count"`    // 总数量
	SuccessCount  int64         `json:"success_count"`  // 成功数量
	ErrorCount    int64         `json:"error_count"`    // 错误数量
	AvgParseTime  time.Duration `json:"avg_parse_time"` // 平均解析时间
	LastFrameTime time.Time     `json:"last_frame"`     // 最后帧时间
	Rate          float64       `json:"rate"`           // 帧率
}

// ResourceUsage 资源使用情况
type ResourceUsage struct {
	CPUPercent     float64 `json:"cpu_percent"`     // CPU使用率
	MemoryUsed     int64   `json:"memory_used"`     // 内存使用量(字节)
	MemoryPercent  float64 `json:"memory_percent"`  // 内存使用率
	GoroutineCount int     `json:"goroutine_count"` // Goroutine数量
	HeapSize       int64   `json:"heap_size"`       // 堆大小
	GCCount        int64   `json:"gc_count"`        // GC次数
}

// SystemError 系统错误信息
type SystemError struct {
	ID         string         `json:"id"`                    // 错误ID
	Type       ErrorType      `json:"type"`                  // 错误类型
	Message    string         `json:"message"`               // 错误消息
	Source     string         `json:"source"`                // 错误源
	Timestamp  time.Time      `json:"timestamp"`             // 发生时间
	Count      int64          `json:"count"`                 // 发生次数
	Severity   ErrorSeverity  `json:"severity"`              // 严重程度
	Resolved   bool           `json:"resolved"`              // 是否已解决
	ResolvedAt *time.Time     `json:"resolved_at,omitempty"` // 解决时间
	Context    map[string]any `json:"context"`               // 错误上下文
}

// ErrorType 错误类型
type ErrorType string

const (
	ErrorTypeConfig  ErrorType = "config"  // 配置错误
	ErrorTypeNetwork ErrorType = "network" // 网络错误
	ErrorTypeParsing ErrorType = "parsing" // 解析错误
	ErrorTypeStorage ErrorType = "storage" // 存储错误
	ErrorTypeSystem  ErrorType = "system"  // 系统错误
	ErrorTypeUnknown ErrorType = "unknown" // 未知错误
)

// ErrorSeverity 错误严重程度
type ErrorSeverity string

const (
	SeverityInfo     ErrorSeverity = "info"     // 信息
	SeverityWarning  ErrorSeverity = "warning"  // 警告
	SeverityError    ErrorSeverity = "error"    // 错误
	SeverityCritical ErrorSeverity = "critical" // 严重
	SeverityFatal    ErrorSeverity = "fatal"    // 致命
)

// UpdateStatus 更新系统状态
func (s *SystemState) UpdateStatus(status ServiceStatus) {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	s.Status = status
	s.Uptime = time.Since(s.StartTime)
	s.LastUpdate = time.Now()
}

// AddError 添加系统错误
func (s *SystemState) AddError(err SystemError) {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	// 检查是否为重复错误
	for i, existingErr := range s.Errors {
		if existingErr.ID == err.ID && existingErr.Type == err.Type && existingErr.Source == err.Source {
			// 更新现有错误
			s.Errors[i].Count++
			s.Errors[i].Timestamp = err.Timestamp
			s.Errors[i].Message = err.Message
			return
		}
	}

	// 添加新错误
	err.Count = 1
	s.Errors = append(s.Errors, err)
	s.LastUpdate = time.Now()

	// 限制错误列表长度，避免内存溢出
	if len(s.Errors) > 1000 {
		s.Errors = s.Errors[len(s.Errors)-1000:]
	}
}

// GetUnresolvedErrors 获取未解决的错误
func (s *SystemState) GetUnresolvedErrors() []SystemError {
	s.mutex.RLock()
	defer s.mutex.RUnlock()

	var unresolved []SystemError
	for _, err := range s.Errors {
		if !err.Resolved {
			unresolved = append(unresolved, err)
		}
	}
	return unresolved
}

// StateManager 系统状态管理器接口
type StateManager interface {
	// GetSystemState 获取系统状态
	GetSystemState() *SystemState

	// UpdateParseStats 更新解析统计
	UpdateParseStats(frameType string, success bool, duration time.Duration)

	// NotifyError 通知错误
	NotifyError(err SystemError)

	// ResolveError 解决错误
	ResolveError(errorID string) error

	// Subscribe 订阅状态变化
	Subscribe(ctx context.Context) <-chan *SystemState

	// IsHealthy 检查系统健康状态
	IsHealthy() bool
}

// EventType SSE事件类型
type EventType string

const (
	EventSystemStateUpdate EventType = "system_state_update" // 系统状态更新
	EventNewFrame          EventType = "new_frame"           // 新帧数据
	EventError             EventType = "error"               // 错误事件
	EventConfigUpdate      EventType = "config_update"       // 配置更新
	EventConnectionUpdate  EventType = "connection_update"   // 连接状态更新
)

// SSEEvent Server-Sent Events 事件
type SSEEvent struct {
	ID        string    `json:"id"`              // 事件ID
	Type      EventType `json:"type"`            // 事件类型
	Data      any       `json:"data"`            // 事件数据
	Timestamp time.Time `json:"timestamp"`       // 事件时间
	Retry     int       `json:"retry,omitempty"` // 重试间隔(毫秒)
}

// String 将SSE事件转换为SSE格式字符串
func (e *SSEEvent) String() string {
	var builder strings.Builder

	if e.ID != "" {
		builder.WriteString(fmt.Sprintf("id: %s\n", e.ID))
	}

	builder.WriteString(fmt.Sprintf("event: %s\n", e.Type))

	if e.Retry > 0 {
		builder.WriteString(fmt.Sprintf("retry: %d\n", e.Retry))
	}

	// 序列化数据
	if e.Data != nil {
		dataBytes, _ := json.Marshal(e.Data)
		lines := strings.Split(string(dataBytes), "\n")
		for _, line := range lines {
			builder.WriteString(fmt.Sprintf("data: %s\n", line))
		}
	}

	builder.WriteString("\n")

	return builder.String()
}
