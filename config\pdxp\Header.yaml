# PDXP协议标准帧头定义
# 此文件定义所有PDXP帧的共有头部结构
version: 3.0

# PDXP数据帧固定格式帧头
header:
  desc: PDXP包头
  class: block
  type: static
  len: 32 # 字节长度
  members:
  - name: Ver
    desc: 版本号
    class: field
    type: uint8
    func: hex
  - name: Mid
    desc: 任务标志
    class: key
    type: uint16
    func: hex
  - name: Sid
    desc: 信源地址
    class: key
    type: uint32
    func: hex
  - name: Did
    desc: 信宿地址
    class: key
    type: uint32
    func: hex
  - name: Bid
    desc: 数据标志
    class: key
    type: uint32
    func: hex
  - name: Num
    desc: 包序号
    class: field
    type: uint32
    func: identity
  - name: Flag
    desc: 数据处理标志
    class: field
    type: uint8
    func: hex
  - name: Reserved
    desc: 保留字段
    class: field
    type: uint32
    func: hex
  - name: Date
    desc: 发送日期
    class: field
    type: uint16
    func: date
  - name: Time
    desc: 发送时标
    class: field
    type: uint32
    func: time
  - name: L
    desc: 数据域长度
    class: field
    type: uint16
    func: identity
