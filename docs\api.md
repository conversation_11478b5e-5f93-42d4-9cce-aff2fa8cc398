# 时序数据库项目前后端接口文档

## 1. 引言

### 1.1 编写目的

前后端接口文档是用于规范前后端开发的文档，确保团队能够准确、快速地进行接口开发和集成。本文档提供前后端接口规范，旨在提高开发效率，降低沟通成本、确保前后端开发能够高效协同。

### 1.2 项目概述

#### 1.2.1 项目背景

本项目基于时序数据库构建，用于处理和展示实时数据流。

#### 1.2.2 项目目标

提供高效、稳定的数据传输和展示服务。

### 1.3 公用规则

#### 1.3.1 命名法

原则上一般采用驼峰命名法，例如 `pageSize`。

#### 1.3.2 计数法

如无特殊约定，传输时小数点后一律保留三位小数。

## 2. 接口清单

| 序号 | 功能 | 说明 |
|------|------|------|
| 1 | 数据大屏页面请求 | 数据大屏页面请求 |

## 3. 接口详情

### 3.1 数据大屏页面查询分析

#### 3.1.1 功能说明

用于数据大屏的信息传输功能。

#### 3.1.2 接口说明

- **请求方式：** POST
- **请求地址：** 待定
- **请求参数：** 见下表
- **响应参数：** 见下表

#### 3.1.3 请求参数

| 字段 | 说明 | 类型 | 备注 | 必填 |
|------|------|------|------|------|
| AttitudeAngle | 姿态角的遥测代号 | Array | 例如 `[16021,16022,16033]`，对应返回参数中的姿态角1、2、3 | 是 |
| event | 所有特征事件的遥测代号 | Array | 同上 | 是 |
| integratedTrajectory | 所有整合弹道弹道号 | Array | 同上 | 是 |
| deviceDetails | 遥外测设备的丢包、帧频 | List | 具体见下表 | 是 |

**deviceDetails 结构：**

| 字段 | 说明 | 类型 | 备注 | 必填 |
|------|------|------|------|------|
| sid | 设备的SID | String | - | 是 |
| devStream | 设备流号 | Number | 0表示该设备为外测设备（1以上数字代表遥测设备流号） | 是 |

#### 3.1.4 返回参数

**响应结构：**

| 字段 | 说明 | 类型 | 备注 | 必填 |
|------|------|------|------|------|
| code | 接口返回状态码 | Number | 成功：1；失败：0 | 是 |
| data | 传输数据 | Object | 具体内容见下表 | 是 |
| message | 信息 | String | - | 否 |

**data 结构：**

| 字段 | 说明 | 类型 | 备注 | 必填 |
|------|------|------|------|------|
| T0 | 起飞时间 | Number | 绝对时微秒数 | 是 |
| trel | 相对时 | Number | 相对时间 | 是 |
| deviceOnline | 在线设备 | List | 所有在线的设备 | 是 |
| deviceInCommunication | 信息传输设备 | List | 当前有信息传输的设备 | 是 |
| V | 弹道的速度 | Number | 自动融合弹道（pn=7）方案弹道帧 | 是 |
| H | 弹道的高度 | Number | 自动融合弹道（pn=7）方案弹道帧 | 是 |
| LL | 经、纬度 | List | 自动融合弹道（pn=7）方案弹道帧 | 是 |
| ZTJ1 | 姿态角1 | Number | - | 是 |
| ZTJ2 | 姿态角2 | Number | - | 是 |
| ZTJ3 | 姿态角3 | Number | - | 是 |
| event | 特征事件 | List | 具体内容见下表 | 是 |
| integratedTrajectory | 整合弹道段落 | List | 具体内容见下表 | 是 |
| deviceInformation | 设备详情 | List | 具体内容见下表 | 是 |

## 4. 数据结构定义

### 4.1 deviceOnline 结构

| 字段 | 说明 | 类型 | 备注 | 必填 |
|------|------|------|------|------|
| id | 序号码 | Number | - | 是 |
| sid | 信源 | String | 设备的sid | 是 |
| ZT | 状态码 | Number | 0:为正常、1为异常 | 是 |

### 4.2 deviceInCommunication 结构

| 字段 | 说明 | 类型 | 备注 | 必填 |
|------|------|------|------|------|
| id | 序号码 | Number | - | 是 |
| sid | 信源 | String | 设备的sid | 是 |

### 4.3 LL 结构

| 字段 | 说明 | 类型 | 备注 | 必填 |
|------|------|------|------|------|
| value | 经纬度值 | String | 格式：`[111.6062,38.83681]` | 是 |

### 4.4 event 结构

| 字段 | 说明 | 类型 | 备注 | 必填 |
|------|------|------|------|------|
| id | 序号码 | Number | 按发生时间排序 | 是 |
| resultId | 遥测代号 | String | - | 是 |
| realTime | 发生时间 | String | 传输相对时，例如 `"192.115"`，每个已发生过的特征事件都要传输 | 是 |

### 4.5 integratedTrajectory 结构

| 字段 | 说明 | 类型 | 备注 | 必填 |
|------|------|------|------|------|
| time | 相对时 | Number | 相对时间 | 是 |
| device | 存在的整合弹道弹道号 | Array | 例如 `["7","8","107"...]` | 是 |

### 4.6 deviceInformation 结构

| 字段 | 说明 | 类型 | 备注 | 必填 |
|------|------|------|------|------|
| packetLossRate | 丢包率 | Number | - | 是 |
| frameRate | 帧频 | Number | - | 是 |

## 5. 示例

### 5.1 请求示例

```json
{
  "AttitudeAngle": [16021, 16022, 16033],
  "event": ["event1", "event2"],
  "integratedTrajectory": ["7", "8", "107"],
  "deviceDetails": [
    {
      "sid": "device001",
      "devStream": 0
    }
  ]
}
```

### 5.2 响应示例

```json
{
  "code": 1,
  "message": "success",
  "data": {
    "T0": 1640995200000000,
    "trel": 192.115,
    "deviceOnline": [
      {
        "id": 1,
        "sid": "device001",
        "ZT": 0
      }
    ],
    "deviceInCommunication": [
      {
        "id": 1,
        "sid": "device001"
      }
    ],
    "V": 1500.123,
    "H": 10000.456,
    "LL": {
      "value": "[111.6062,38.83681]"
    },
    "ZTJ1": 45.123,
    "ZTJ2": 90.456,
    "ZTJ3": 180.789,
    "event": [
      {
        "id": 1,
        "resultId": "16021",
        "realTime": "192.115"
      }
    ],
    "integratedTrajectory": [
      {
        "time": 192.115,
        "device": ["7", "8", "107"]
      }
    ],
    "deviceInformation": [
      {
        "packetLossRate": 0.01,
        "frameRate": 60
      }
    ]
  }
}
```

## 6. 错误码说明

| 错误码 | 说明 |
|--------|------|
| 0 | 请求失败 |
| 1 | 请求成功 |

## 7. 注意事项

1. 所有时间相关字段均使用相对时间或绝对时微秒数
2. 数组类型字段在无数据时返回空数组 `[]`
3. 所有数值类型字段保留三位小数
4. 设备状态码：0表示正常，1表示异常
5. 外测设备的 `devStream` 为 0，遥测设备为 1 以上数字
