# Aquila PDXP Backend 完整配置文件
# 基于设计文档的企业级配置

# ============================================================================
# UDP 组播配置
# ============================================================================
udp:
- name: tracking_primary
  addr: 230.25.1.1
  port: 2511
  desc: 主平面外测数据
  enable: true
- name: tracking_secondary
  addr: 230.25.1.2
  port: 2512
  desc: 二平面外测数据
  enable: true
- name: telemetry_plaintext
  addr: 230.25.1.3
  port: 2513
  desc: 遥测明文数据
  enable: true
- name: statistic_infomation
  addr: 230.25.1.4
  port: 2514
  desc: 统计信息
  enable: true
- name: trajectory_primary
  addr: 230.25.2.1
  port: 2521
  desc: 主平面方案弹道、偏差弹道、卫星轨根
  enable: true
- name: trajectory_secondary
  addr: 230.25.2.2
  port: 2522
  desc: 二平面方案弹道
  enable: true
- name: phased_array_radar_original
  addr: 230.25.2.3
  port: 2523
  desc: 相控阵雷达原始数据
  enable: true
- name: telemetry_trajectory_original
  addr: 230.25.3.1
  port: 2531
  desc: 遥测原始结果弹道、GNSS差分结果、GNSS自定位原始弹道
  enable: true
- name: telemetry_parameter
  addr: 230.25.3.2
  port: 2532
  desc: 遥测参数帧、特征点弹道、遥测T0
  enable: true
- name: gnss_original
  addr: 230.25.3.3
  port: 2533
  desc: 云平台输出的GNSS原始测量信息
  enable: true
- name: send_to_data
  addr: 230.25.5.1
  port: 2551
  desc: 发送数据
  enable: true
- name: system_control
  addr: 230.25.9.1
  port: 2591
  desc: 系统控制命令、进程控制命令
  enable: true
- name: interactive_control
  addr: 230.25.9.2
  port: 2592
  desc: 交互控制命令
  enable: true
- name: integral_fused_control
  addr: 230.25.9.3
  port: 2593
  desc: 积分控制命令、融合控制命令
  enable: true
- name: trajectory_priority_control
  addr: 230.25.9.4
  port: 2594
  desc: 方案优先顺序命令
  enable: true
- name: fall_point
  addr: 230.25.9.5
  port: 2595
  desc: 落点方案弹道
  enable: true
- name: guidance_control
  addr: 230.25.9.6
  port: 2596
  desc: 导调控制命令
  enable: true
- name: preset_t0
  addr: 230.25.9.7
  port: 2597
  desc: 窗口预设T0
  enable: true
- name: software_monitor
  addr: 230.25.12.1
  port: 25121
  desc: 软件运行状态、运行监视信息
  enable: true
- name: ka_control
  addr: 225.25.1.6
  port: 9800
  desc: Ka控制命令
  enable: true
- name: gnss_benchmark
  addr: 225.32.0.17
  port: 24588
  desc: 中心GNSS基准信息、中心遥测挑点输出的GNSS原始测量信息
  enable: true
- name: gnss_704
  addr: 226.25.1.1
  port: 5001
  desc: 704所GNSS差分软件输出的GNSS差分结果
  enable: true

# ============================================================================
# PDXP 协议配置
# ============================================================================
pdxp: # PDXP协议帧类型配置
  # name: 真类型的唯一名称，不可重复
  # key: 帧结构定义的唯一名称，不同帧类型可使用相同帧结构
- name: gx_pri
  key: GX
  desc: 一平面光学设备测量帧
  src: tracking_primary
  enable: true
  bids:
  - 0x00100101
  - 0x00100102
  - 0x00100103
  - 0x00100104
  - 0x00100105
  - 0x00100106
- name: gx_sec
  key: GX
  desc: 二平面光学设备测量帧
  src: tracking_secondary
  enable: true
  bids:
  - 0x00100101
  - 0x00100102
  - 0x00100103
  - 0x00100104
  - 0x00100105
  - 0x00100106
- name: ml_pri
  key: ML
  desc: 一平面脉冲激光雷达测量帧
  src: tracking_primary
  enable: true
  bids:
  - 0x00100404
  - 0x00100401
- name: ml_sec
  key: ML
  desc: 二平面脉冲激光雷达测量帧
  src: tracking_secondary
  enable: true
  bids:
  - 0x00100404
  - 0x00100401
- name: ld_pri
  key: LD
  desc: 一平面连续激光雷达测量帧
  src: tracking_primary
  enable: true
  bids:
  - 0x00100501
- name: ld_sec
  key: LD
  desc: 二平面连续激光雷达测量帧
  src: tracking_secondary
  enable: true
  bids:
  - 0x00100501
- name: ka_ae_pri
  key: KaAE
  desc: 一平面Ka测角测量帧
  src: tracking_primary
  enable: true
  bids:
  - 0x00100615
  - 0x00100635
- name: ka_ae_sec
  key: KaAE
  desc: 二平面Ka测角测量帧
  src: tracking_secondary
  enable: true
  bids:
  - 0x00100615
  - 0x00100635
- name: ka_r_pri
  key: KaR
  desc: 一平面Ka测距测量帧
  src: tracking_primary
  enable: true
  bids:
  - 0x00100611
  - 0x00100631
- name: ka_r_sec
  key: KaR
  desc: 二平面Ka测距测量帧
  src: tracking_secondary
  enable: true
  bids:
  - 0x00100611
  - 0x00100631
- name: ka_v_pri
  key: KaV
  desc: 一平面Ka测速测量帧
  src: tracking_primary
  enable: true
  bids:
  - 0x00100613
  - 0x00100633
- name: ka_v_sec
  key: KaV
  desc: 二平面Ka测速测量帧
  src: tracking_secondary
  enable: true
  bids:
  - 0x00100613
  - 0x00100633
- name: zkt_pri
  key: ZKT
  desc: 一平面载控通测量帧
  src: tracking_primary
  enable: true
  bids:
  - 0x00100100
- name: zkt_sec
  key: ZKT
  desc: 二平面载控通测量帧
  src: tracking_secondary
  enable: true
  bids:
  - 0x00100100
- name: xl
  key: XL
  desc: 相控阵雷达测量帧
  src: tracking_primary
  enable: true
  bids:
  - 0x00100405
- name: launch_t0
  key: T0
  desc: 阵地起飞信号帧
  src: tracking_primary
  enable: true
  bids:
  - 0x00010100
- name: inter_base
  key: Bases
  desc: 基地间数据帧
  src: tracking_primary
  enable: true
  bids:
  - 0x00020501
  - 0x00010101
  - 0x002A0121
  - 0x002A0122
  - 0x00110210
  - 0x002A0100
  - 0x00010212
  - 0x002A0125
  - 0x00470301
  - 0x00020560
  - 0x002A0127
  - 0x002F0101
  - 0x002A0102
  - 0x00110111
  - 0x002A0301
  - 0x002A0105
  - 0x002A0106
  - 0x002A0171
  - 0x002A0172
  - 0x002A0173
  - 0x002B0101
  - 0x002A0111
  - 0x002D0402
  - 0x002A0112
  - 0x002B0111
  - 0x002B0122
  - 0x002D0110
  - 0x002A0152
  - 0x002A0162
  - 0x00020551
  - 0x002D0203
  - 0x0000EE00
  - 0x002B011F
  - 0x002B012F
  - 0x002B0121
  - 0x00200111
  - 0x002A0101
  - 0x002A0212
  - 0x002A0202
  - 0x002A0203
  - 0x002A0217
  - 0x002A0218
  - 0x002A0219
  - 0x002A021A
  - 0x002A021B
  - 0x002A021C
  - 0x00020301
  - 0x00020402
  - 0x002A0401
  - 0x002A0402
  - 0x002A0201
- name: ka_ctrl
  key: KaCtrl
  desc: Ka控制状态帧
  src: tracking_primary
  enable: true
  bids:
  - 0x00020531
  - 0x00020551
  - 0x00200112
- name: tele_ori
  key: Tele
  desc: 遥测原始数据帧
  src: telemetry_plaintext
  enable: true
  bids:
  - 0x00110101
- name: mobile_station
  key: Mobile
  desc: 机动站址帧
  src: tracking_primary
  enable: true
  bids:
  - 0x00010212
- name: track
  key: Track
  desc: 跟踪测量帧
  src: tracking_primary
  enable: true
  bids:
  - 0x00010211
- name: link_mon_pri
  key: LinkMon
  desc: 一平面设备链路监视帧
  src: tracking_primary
  enable: true
  bids:
  - 0x00020101
- name: link_mon_sec
  key: LinkMon
  desc: 二平面设备链路监视帧
  src: tracking_primary
  enable: true
  bids:
  - 0xAA020101
- name: stat_info
  key: Stat
  desc: 统计信息帧
  src: statistic_infomation
  enable: true
  bids:
  - 0x25010001
  - 0x25010002
  - 0x25010003
  - 0x25010004
  - 0x25010005
- name: traj_pri
  key: Traj
  desc: 一平面方案弹道帧
  src: trajectory_primary
  enable: true
  bids:
  - 0x25020001
- name: traj_sec
  key: Traj
  desc: 二平面方案弹道帧
  src: trajectory_secondary
  enable: true
  bids:
  - 0x25020001
- name: diff_traj_pri
  key: DiffTraj
  desc: 一平面偏差弹道帧
  src: trajectory_primary
  enable: true
  bids:
  - 0x25020003
- name: diff_traj_sec
  key: DiffTraj
  desc: 二平面偏差弹道帧
  src: trajectory_secondary
  enable: true
  bids:
  - 0x25020003
- name: sat_orbit_pri
  key: Orbit
  desc: 一平面卫星轨根帧
  src: trajectory_primary
  enable: true
  bids:
  - 0x25020002
- name: sat_orbit_sec
  key: Orbit
  desc: 二平面卫星轨根帧
  src: trajectory_secondary
  enable: true
  bids:
  - 0x25020002
- name: tele_traj_ori
  key: TeleTraj
  desc: 遥测原始结果弹道
  src: telemetry_trajectory_original
  enable: true
  bids:
  - 0x25030010
  - 0x25030011
  - 0x250300E0
  - 0x250300E1
  - 0x250300E2
  - 0x250300E3
  - 0x250300F0
  - 0x250300F1
  - 0x250300F2
  - 0x250300F3
- name: tele_t0
  key: TeleT0
  desc: 遥测T0帧
  src: telemetry_parameter
  enable: true
  bids:
  - 0x25030001
- name: tele_param
  key: Param
  desc: 遥测参数帧
  src: telemetry_parameter
  enable: true
  bids:
  - 0x25030002
- name: tele_dev
  key: TeleDev
  desc: 遥测设备状态帧
  src: tracking_primary
  enable: true
  bids:
  - 0x25030003
- name: tele_char_traj
  key: CharTraj
  desc: 遥测特征点弹道帧
  src: telemetry_parameter
  enable: true
  bids:
  - 0x25030004
- name: gnss_traj
  key: GnssTraj
  desc: 云平台GNSS弹道帧
  src: telemetry_trajectory_original
  enable: true
  bids:
  - 0x002F0113
  - 0x002F0115
- name: data_send_frame
  key: DS
  desc: 发送数据帧
  src: send_to_data
  enable: true
  bids:
  - 0x00020501
  - 0x00010100
  - 0x00020301
  - 0x002A0217
  - 0x00110210
  - 0x00020550
  - 0x002A0111
  - 0x00010212
  - 0x002A0172
  - 0x002A0173
  - 0x002A0171
  - 0x002F0101
  - 0x002A0100
  - 0x002A0301
  - 0x00110111
  - 0x00020551
  - 0x00020560
  - 0x00470301
  - 0x002A0101
  - 0x002A0102
  - 0x002A0103
  - 0x002A0105
  - 0x002A0106
  - 0x002A0107
  - 0x002D0402
  - 0x00010211
  - 0x002A0112
  - 0x002A0122
  - 0x002B0101
  - 0x002A0115
  - 0x002A0116
  - 0x002A0121
  - 0x002A0132
  - 0x002A0142
  - 0x002B0111
  - 0x002B0112
  - 0x002B011F
  - 0x002B0121
  - 0x00200111
  - 0x00200112
  - 0x002B012F
  - 0x002B0131
  - 0x002D0110
  - 0x002D0111
  - 0x002D0112
  - 0x002D0113
  - 0x002D0116
  - 0x002D0203
- name: gnss_bench
  key: GnssBench
  desc: GNSS基准信息帧
  src: gnss_benchmark
  enable: true
  bids:
  - 0x00010201
  - 0x00010202
  - 0x00010203
  - 0x00010204
  - 0x00010205
  - 0x00010206
- name: gnss_original
  key: GnssOri
  desc: GNSS原始测量信息帧
  src: gnss_benchmark
  enable: true
  bids:
  - 0x002F0114
  - 0x002F0116
- name: gnss_original_cloud
  key: Gnss
  desc: 云平台GNSS原始测量信息帧
  src: gnss_original
  enable: true
  bids:
  - 0x002F0114
  - 0x002F0116
- name: gnss_704_traj
  key: Gnss704Traj
  desc: 704所GNSS差分结果帧
  src: gnss_704
  enable: true
  bids:
  - 0x002F0113
  - 0x002F0115
- name: sys_ctrl
  key: Sys
  desc: 系统控制命令帧
  src: system_control
  enable: true
  bids:
  - 0x25090001
- name: proc_ctrl
  key: ProcCtrl
  desc: 进程控制命令帧
  src: system_control
  enable: true
  bids:
  - 0x25090002
- name: inter_ctrl
  key: Inter
  desc: 交互控制命令帧
  src: interactive_control
  enable: true
  bids:
  - 0x25090003
- name: integ_ctrl
  key: Integ
  desc: 积分控制命令帧
  src: integral_fused_control
  enable: true
  bids:
  - 0x25090004
- name: fuse_ctrl
  key: Fuse
  desc: 融合控制命令帧
  src: integral_fused_control
  enable: true
  bids:
  - 0x25090005
- name: prio_ctrl
  key: Prio
  desc: 方案优先顺序命令帧
  src: trajectory_priority_control
  enable: true
  bids:
  - 0x25090006
- name: fp_ctrl
  key: FP
  desc: 落点方案弹道命令帧
  src: fall_point
  enable: true
  bids:
  - 0x25090007
- name: guide_ctrl
  key: Guide
  desc: 导调控制命令帧
  src: guidance_control
  enable: true
  bids:
  - 0x25090008
- name: pre_t0
  key: PreT0
  desc: 窗口预设T0命令帧
  src: preset_t0
  enable: true
  bids:
  - 0x25090009
- name: proc_status
  key: ProcStat
  desc: 进程状态监控帧
  src: software_monitor
  enable: true
  bids:
  - 0x250A0001
- name: proc_mon
  key: ProcMon
  desc: 进程监控信息帧
  src: software_monitor
  enable: true
  bids:
  - 0x250A0002

# ============================================================================
# 线程池配置
# ============================================================================
thread_pool:
  # PDXP解析线程池
  pdxp_workers: 16              # PDXP解析工作线程数
  pdxp_queue_size: 10000        # PDXP解析队列大小
  pdxp_timeout: 5s              # PDXP解析超时时间

  # UDP监听线程池
  udp_workers: 8                # UDP监听工作线程数
  udp_buffer_size: 65536        # UDP缓冲区大小
  udp_timeout: 1s               # UDP读取超时时间

  # 通用配置
  max_idle_time: 30s            # 最大空闲时间
  graceful_stop: 10s            # 优雅停止超时时间

# ============================================================================
# Redis 配置
# ============================================================================
redis:
  # 连接配置
  host: "localhost"             # Redis主机地址
  port: 6379                    # Redis端口
  password: ""                  # Redis密码
  db: 0                         # Redis数据库编号

  # 连接池配置
  pool_size: 20                 # 连接池大小
  min_idle_conns: 5             # 最小空闲连接数
  max_retries: 3                # 最大重试次数
  dial_timeout: 5s              # 连接超时时间
  read_timeout: 3s              # 读取超时时间
  write_timeout: 3s             # 写入超时时间

  # 时间序列配置
  ts_retention: 168h            # 时间序列数据保留时间(7天)
  ts_batch_size: 1000           # 批量写入大小
  ts_batch_timeout: 100ms       # 批量写入超时时间
  ts_key_prefix: "aquila:ts:"   # 时间序列键前缀

# ============================================================================
# HTTP 服务配置
# ============================================================================
http:
  # 服务器配置
  host: "0.0.0.0"               # 监听地址
  port: 8080                    # 监听端口
  read_timeout: 30s             # 读取超时时间
  write_timeout: 30s            # 写入超时时间
  idle_timeout: 120s            # 空闲超时时间

  # CORS配置
  cors:
    enabled: true               # 是否启用CORS
    allow_origins:              # 允许的源
      - "*"
    allow_methods:              # 允许的方法
      - "GET"
      - "POST"
      - "PUT"
      - "DELETE"
      - "OPTIONS"
    allow_headers:              # 允许的头部
      - "Content-Type"
      - "Authorization"
      - "X-Requested-With"
    max_age: 86400              # 预检请求缓存时间(24小时)

  # API配置
  api:
    # 分页配置
    default_page_size: 100      # 默认分页大小
    max_page_size: 1000         # 最大分页大小

    # 查询配置
    max_time_range: 24h         # 最大时间范围
    default_time_span: 1h       # 默认时间跨度

    # 限流配置
    rate_limit:
      enabled: true             # 是否启用限流
      rps: 100.0                # 每秒请求数限制
      burst: 200                # 突发请求数限制

  # TLS配置（可选）
  # tls:
  #   cert_file: "/path/to/cert.pem"
  #   key_file: "/path/to/key.pem"

# ============================================================================
# SSE (Server-Sent Events) 配置
# ============================================================================
sse:
  # 连接配置
  max_connections: 100          # 最大并发连接数
  buffer_size: 1000             # 事件缓冲区大小
  ping_interval: 30s            # 心跳间隔
  write_timeout: 10s            # 写入超时时间

  # 推送配置
  push_frequency: 100ms         # 推送频率(10Hz)
  batch_size: 50                # 批量推送大小
  filter_enabled: true          # 是否启用过滤

  # 重连配置
  retry_interval: 5s            # 重连间隔
  max_retries: 3                # 最大重试次数

# ============================================================================
# 监控指标配置
# ============================================================================
metrics:
  # Prometheus配置
  enabled: true                 # 是否启用指标收集
  path: "/metrics"              # 指标路径
  namespace: "aquila"           # 指标命名空间
  subsystem: "pdxp_backend"     # 指标子系统

  # 收集配置
  collect_interval: 15s         # 收集间隔
  histogram_buckets:            # 直方图桶配置
    - 0.001
    - 0.005
    - 0.01
    - 0.025
    - 0.05
    - 0.1
    - 0.25
    - 0.5
    - 1.0
    - 2.5
    - 5.0
    - 10.0

  # 系统指标
  system_metrics:
    cpu: true                   # 是否收集CPU指标
    memory: true                # 是否收集内存指标
    disk: true                  # 是否收集磁盘指标
    network: true               # 是否收集网络指标
    goroutine: true             # 是否收集Goroutine指标

# ============================================================================
# 日志配置
# ============================================================================
logging:
  # 基础配置
  level: "info"                 # 日志级别 (debug/info/warn/error)
  format: "json"                # 日志格式 (json/text)

  # 输出配置
  output:                       # 输出目标
    - "stdout"
    - "file"
  filename: "logs/aquila.log"   # 日志文件名

  # 轮转配置
  rotation:
    enabled: true               # 是否启用轮转
    max_size: 100               # 最大文件大小(MB)
    max_age: 30                 # 最大保留天数
    max_backups: 10             # 最大备份文件数
    compress: true              # 是否压缩

  # 结构化日志配置
  structured:
    enabled: true               # 是否启用结构化日志
    fields:                     # 包含的字段
      - "timestamp"
      - "level"
      - "message"
      - "component"
      - "trace_id"
    exclude_keys:               # 排除的键
      - "password"
      - "secret"
    time_format: "2006-01-02T15:04:05.000Z07:00" # 时间格式

# ============================================================================
# 系统配置
# ============================================================================
system:
  # 应用信息
  name: "Aquila PDXP Backend"   # 应用名称
  version: "1.0.0"              # 应用版本
  environment: "production"     # 运行环境 (development/testing/production)

  # 性能配置
  max_procs: 0                  # 最大处理器数 (0=自动检测)
  gc_percent: 100               # GC百分比
  memory_limit: **********      # 内存限制(8GB)

  # 健康检查配置
  health_check:
    enabled: true               # 是否启用健康检查
    path: "/health"             # 健康检查路径
    interval: 30s               # 检查间隔
    timeout: 5s                 # 检查超时时间

  # 优雅关闭配置
  graceful_shutdown:
    timeout: 30s                # 关闭超时时间
    wait_signals:               # 等待的信号
      - "SIGTERM"
      - "SIGINT"

# ============================================================================
# 配置文件说明
# ============================================================================
#
# 本配置文件基于 Aquila PDXP Backend 设计文档，包含以下主要组件：
#
# 1. UDP组播监听：支持多个组播地址并发监听
# 2. PDXP协议解析：基于BID的帧类型识别和解析
# 3. 线程池管理：高性能并发处理架构
# 4. Redis时间序列：数据缓存和状态管理
# 5. HTTP REST API：数据查询和系统管理接口
# 6. SSE实时推送：状态变更实时通知
# 7. 监控指标：Prometheus指标收集
# 8. 结构化日志：完整的日志记录和轮转
# 9. 系统管理：健康检查和优雅关闭
#
# 性能目标：
# - UDP数据接收：>= 10,000 packets/sec
# - PDXP协议解析：>= 80,000 frames/sec
# - HTTP SSE推送：>= 100 并发连接
# - 端到端延迟：< 50ms (P99)
#
# 更多信息请参考：
# - 设计文档：.augment/rules/design.md
# - 产品文档：.augment/rules/product.md
# - 技术架构：.augment/rules/tech.md
# ============================================================================
