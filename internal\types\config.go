// Package types 定义Aquila系统的配置类型
package types

import (
	"time"
)

// AppConfig 应用程序完整配置结构
type AppConfig struct {
	// 基础配置
	UDP  []UDPEndpoint   `yaml:"udp" json:"udp"`   // UDP组播配置
	PDXP []PDXPFrameType `yaml:"pdxp" json:"pdxp"` // PDXP协议配置

	// 新增组件配置
	ThreadPool ThreadPoolConfig `yaml:"thread_pool" json:"thread_pool"` // 线程池配置
	Redis      RedisConfig      `yaml:"redis" json:"redis"`             // Redis配置
	HTTP       HTTPConfig       `yaml:"http" json:"http"`               // HTTP服务配置
	SSE        SSEConfig        `yaml:"sse" json:"sse"`                 // SSE配置
	Metrics    MetricsConfig    `yaml:"metrics" json:"metrics"`         // 监控指标配置
	Logging    LoggingConfig    `yaml:"logging" json:"logging"`         // 日志配置
	System     SystemConfig     `yaml:"system" json:"system"`           // 系统配置
}

// ThreadPoolConfig 线程池配置
type ThreadPoolConfig struct {
	// PDXP解析线程池
	PDXPWorkers    int           `yaml:"pdxp_workers" json:"pdxp_workers"`       // PDXP解析工作线程数
	PDXPQueueSize  int           `yaml:"pdxp_queue_size" json:"pdxp_queue_size"` // PDXP解析队列大小
	PDXPTimeout    time.Duration `yaml:"pdxp_timeout" json:"pdxp_timeout"`       // PDXP解析超时时间
	
	// UDP监听线程池
	UDPWorkers     int           `yaml:"udp_workers" json:"udp_workers"`         // UDP监听工作线程数
	UDPBufferSize  int           `yaml:"udp_buffer_size" json:"udp_buffer_size"` // UDP缓冲区大小
	UDPTimeout     time.Duration `yaml:"udp_timeout" json:"udp_timeout"`         // UDP读取超时时间
	
	// 通用配置
	MaxIdleTime    time.Duration `yaml:"max_idle_time" json:"max_idle_time"`     // 最大空闲时间
	GracefulStop   time.Duration `yaml:"graceful_stop" json:"graceful_stop"`     // 优雅停止超时时间
}

// RedisConfig Redis配置
type RedisConfig struct {
	// 连接配置
	Host     string `yaml:"host" json:"host"`         // Redis主机地址
	Port     int    `yaml:"port" json:"port"`         // Redis端口
	Password string `yaml:"password" json:"password"` // Redis密码
	DB       int    `yaml:"db" json:"db"`             // Redis数据库编号
	
	// 连接池配置
	PoolSize     int           `yaml:"pool_size" json:"pool_size"`         // 连接池大小
	MinIdleConns int           `yaml:"min_idle_conns" json:"min_idle_conns"` // 最小空闲连接数
	MaxRetries   int           `yaml:"max_retries" json:"max_retries"`     // 最大重试次数
	DialTimeout  time.Duration `yaml:"dial_timeout" json:"dial_timeout"`   // 连接超时时间
	ReadTimeout  time.Duration `yaml:"read_timeout" json:"read_timeout"`   // 读取超时时间
	WriteTimeout time.Duration `yaml:"write_timeout" json:"write_timeout"` // 写入超时时间
	
	// 时间序列配置
	TSRetention    time.Duration `yaml:"ts_retention" json:"ts_retention"`       // 时间序列数据保留时间
	TSBatchSize    int           `yaml:"ts_batch_size" json:"ts_batch_size"`     // 批量写入大小
	TSBatchTimeout time.Duration `yaml:"ts_batch_timeout" json:"ts_batch_timeout"` // 批量写入超时时间
	TSKeyPrefix    string        `yaml:"ts_key_prefix" json:"ts_key_prefix"`     // 时间序列键前缀
}

// HTTPConfig HTTP服务配置
type HTTPConfig struct {
	// 服务器配置
	Host         string        `yaml:"host" json:"host"`                   // 监听地址
	Port         int           `yaml:"port" json:"port"`                   // 监听端口
	ReadTimeout  time.Duration `yaml:"read_timeout" json:"read_timeout"`   // 读取超时时间
	WriteTimeout time.Duration `yaml:"write_timeout" json:"write_timeout"` // 写入超时时间
	IdleTimeout  time.Duration `yaml:"idle_timeout" json:"idle_timeout"`   // 空闲超时时间
	
	// CORS配置
	CORS CORSConfig `yaml:"cors" json:"cors"` // 跨域配置
	
	// API配置
	API APIConfig `yaml:"api" json:"api"` // API配置
	
	// TLS配置（可选）
	TLS *TLSConfig `yaml:"tls,omitempty" json:"tls,omitempty"` // TLS配置
}

// CORSConfig 跨域配置
type CORSConfig struct {
	Enabled      bool     `yaml:"enabled" json:"enabled"`             // 是否启用CORS
	AllowOrigins []string `yaml:"allow_origins" json:"allow_origins"` // 允许的源
	AllowMethods []string `yaml:"allow_methods" json:"allow_methods"` // 允许的方法
	AllowHeaders []string `yaml:"allow_headers" json:"allow_headers"` // 允许的头部
	MaxAge       int      `yaml:"max_age" json:"max_age"`             // 预检请求缓存时间
}

// APIConfig API配置
type APIConfig struct {
	// 分页配置
	DefaultPageSize int `yaml:"default_page_size" json:"default_page_size"` // 默认分页大小
	MaxPageSize     int `yaml:"max_page_size" json:"max_page_size"`         // 最大分页大小
	
	// 查询配置
	MaxTimeRange    time.Duration `yaml:"max_time_range" json:"max_time_range"`       // 最大时间范围
	DefaultTimeSpan time.Duration `yaml:"default_time_span" json:"default_time_span"` // 默认时间跨度
	
	// 限流配置
	RateLimit RateLimitConfig `yaml:"rate_limit" json:"rate_limit"` // 限流配置
}

// RateLimitConfig 限流配置
type RateLimitConfig struct {
	Enabled bool    `yaml:"enabled" json:"enabled"` // 是否启用限流
	RPS     float64 `yaml:"rps" json:"rps"`         // 每秒请求数限制
	Burst   int     `yaml:"burst" json:"burst"`     // 突发请求数限制
}

// TLSConfig TLS配置
type TLSConfig struct {
	CertFile string `yaml:"cert_file" json:"cert_file"` // 证书文件路径
	KeyFile  string `yaml:"key_file" json:"key_file"`   // 私钥文件路径
}

// SSEConfig Server-Sent Events配置
type SSEConfig struct {
	// 连接配置
	MaxConnections int           `yaml:"max_connections" json:"max_connections"` // 最大并发连接数
	BufferSize     int           `yaml:"buffer_size" json:"buffer_size"`         // 事件缓冲区大小
	PingInterval   time.Duration `yaml:"ping_interval" json:"ping_interval"`     // 心跳间隔
	WriteTimeout   time.Duration `yaml:"write_timeout" json:"write_timeout"`     // 写入超时时间
	
	// 推送配置
	PushFrequency  time.Duration `yaml:"push_frequency" json:"push_frequency"`   // 推送频率
	BatchSize      int           `yaml:"batch_size" json:"batch_size"`           // 批量推送大小
	FilterEnabled  bool          `yaml:"filter_enabled" json:"filter_enabled"`   // 是否启用过滤
	
	// 重连配置
	RetryInterval time.Duration `yaml:"retry_interval" json:"retry_interval"` // 重连间隔
	MaxRetries    int           `yaml:"max_retries" json:"max_retries"`       // 最大重试次数
}

// MetricsConfig 监控指标配置
type MetricsConfig struct {
	// Prometheus配置
	Enabled    bool   `yaml:"enabled" json:"enabled"`       // 是否启用指标收集
	Path       string `yaml:"path" json:"path"`             // 指标路径
	Namespace  string `yaml:"namespace" json:"namespace"`   // 指标命名空间
	Subsystem  string `yaml:"subsystem" json:"subsystem"`   // 指标子系统
	
	// 收集配置
	CollectInterval time.Duration `yaml:"collect_interval" json:"collect_interval"` // 收集间隔
	HistogramBuckets []float64    `yaml:"histogram_buckets" json:"histogram_buckets"` // 直方图桶配置
	
	// 系统指标
	SystemMetrics SystemMetricsConfig `yaml:"system_metrics" json:"system_metrics"` // 系统指标配置
}

// SystemMetricsConfig 系统指标配置
type SystemMetricsConfig struct {
	CPU       bool `yaml:"cpu" json:"cpu"`             // 是否收集CPU指标
	Memory    bool `yaml:"memory" json:"memory"`       // 是否收集内存指标
	Disk      bool `yaml:"disk" json:"disk"`           // 是否收集磁盘指标
	Network   bool `yaml:"network" json:"network"`     // 是否收集网络指标
	Goroutine bool `yaml:"goroutine" json:"goroutine"` // 是否收集Goroutine指标
}

// LoggingConfig 日志配置
type LoggingConfig struct {
	// 基础配置
	Level  string `yaml:"level" json:"level"`   // 日志级别
	Format string `yaml:"format" json:"format"` // 日志格式 (json/text)
	
	// 输出配置
	Output   []string `yaml:"output" json:"output"`     // 输出目标 (stdout/stderr/file)
	Filename string   `yaml:"filename" json:"filename"` // 日志文件名
	
	// 轮转配置
	Rotation LogRotationConfig `yaml:"rotation" json:"rotation"` // 日志轮转配置
	
	// 结构化日志配置
	Structured StructuredLogConfig `yaml:"structured" json:"structured"` // 结构化日志配置
}

// LogRotationConfig 日志轮转配置
type LogRotationConfig struct {
	Enabled    bool   `yaml:"enabled" json:"enabled"`       // 是否启用轮转
	MaxSize    int    `yaml:"max_size" json:"max_size"`     // 最大文件大小(MB)
	MaxAge     int    `yaml:"max_age" json:"max_age"`       // 最大保留天数
	MaxBackups int    `yaml:"max_backups" json:"max_backups"` // 最大备份文件数
	Compress   bool   `yaml:"compress" json:"compress"`     // 是否压缩
}

// StructuredLogConfig 结构化日志配置
type StructuredLogConfig struct {
	Enabled     bool     `yaml:"enabled" json:"enabled"`         // 是否启用结构化日志
	Fields      []string `yaml:"fields" json:"fields"`           // 包含的字段
	ExcludeKeys []string `yaml:"exclude_keys" json:"exclude_keys"` // 排除的键
	TimeFormat  string   `yaml:"time_format" json:"time_format"`  // 时间格式
}

// SystemConfig 系统配置
type SystemConfig struct {
	// 应用信息
	Name        string `yaml:"name" json:"name"`               // 应用名称
	Version     string `yaml:"version" json:"version"`         // 应用版本
	Environment string `yaml:"environment" json:"environment"` // 运行环境
	
	// 性能配置
	MaxProcs       int           `yaml:"max_procs" json:"max_procs"`             // 最大处理器数
	GCPercent      int           `yaml:"gc_percent" json:"gc_percent"`           // GC百分比
	MemoryLimit    int64         `yaml:"memory_limit" json:"memory_limit"`       // 内存限制(字节)
	
	// 健康检查配置
	HealthCheck HealthCheckConfig `yaml:"health_check" json:"health_check"` // 健康检查配置
	
	// 优雅关闭配置
	GracefulShutdown GracefulShutdownConfig `yaml:"graceful_shutdown" json:"graceful_shutdown"` // 优雅关闭配置
}

// HealthCheckConfig 健康检查配置
type HealthCheckConfig struct {
	Enabled  bool          `yaml:"enabled" json:"enabled"`   // 是否启用健康检查
	Path     string        `yaml:"path" json:"path"`         // 健康检查路径
	Interval time.Duration `yaml:"interval" json:"interval"` // 检查间隔
	Timeout  time.Duration `yaml:"timeout" json:"timeout"`   // 检查超时时间
}

// GracefulShutdownConfig 优雅关闭配置
type GracefulShutdownConfig struct {
	Timeout     time.Duration `yaml:"timeout" json:"timeout"`         // 关闭超时时间
	WaitSignals []string      `yaml:"wait_signals" json:"wait_signals"` // 等待的信号
}
